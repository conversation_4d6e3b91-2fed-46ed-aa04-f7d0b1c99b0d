package com.mercaso.ims.application.service.impl;

import static com.mercaso.ims.infrastructure.exception.ErrorCodeEnums.ITEM_COST_COLLECTION_NOT_FOUND;

import com.alibaba.excel.util.DateUtils;
import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.dto.ItemCostCollectionDto;
import com.mercaso.ims.application.dto.payload.ItemCostCollectionCreatedPayloadDto;
import com.mercaso.ims.application.mapper.itemcostcollection.ItemCostCollectionApplicationMapper;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.domain.businessevent.service.BusinessEventService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollectionFactory;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.VendorConstant;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleOrderCollectionDto;
import com.mercaso.ims.infrastructure.external.finale.dto.QueryFinalePurchaseOrderResultDto.PurchaseOrderPageInfo;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;

import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class ItemCostCollectionApplicationServiceImpl implements ItemCostCollectionApplicationService {

    private final ItemCostCollectionService itemCostCollectionService;
    private final ItemCostCollectionApplicationMapper itemCostCollectionApplicationMapper;
    private final BusinessEventService businessEventService;
    private final DocumentApplicationService documentApplicationService;
    private final PgAdvisoryLock pgAdvisoryLock;
    private final EntityManagerFactory managerFactory;
    private final VendorService vendorService;
    private final FinaleExternalApiAdaptor finaleExternalApiAdaptor;


    @Override
    public ItemCostCollectionDto create(CreateItemCostCollectionCommand command) {
        return createItemCostCollection(command);
    }

    @Override
    @Async
    public void createJetroCostCollection(byte[] content) {
        log.info("Start to create Jetro cost collection,.");
        String collectionNumber = DateUtils.format(new Date(), DateUtils.DATE_FORMAT_14);
        String fileName = collectionNumber
            .concat(".xlsx");
        DocumentResponse documentResponse = documentApplicationService.uploadFileContent(content,
            fileName,
            false);
        Vendor vendor = vendorService.findByVendorName(VendorConstant.JETRO);

        CreateItemCostCollectionCommand command = CreateItemCostCollectionCommand.builder()
            .vendorId(vendor.getId())
            .vendorName(vendor.getVendorName())
            .vendorCollectionNumber(collectionNumber)
            .fileName(documentResponse.getName())
            .source(ItemCostCollectionSources.JETRO_DAILY_ITEM_LISTS)
            .type(ItemCostCollectionTypes.EXCEL_FILE)
            .build();
        createItemCostCollection(command);
        log.info("End to create Jetro cost collection");
    }

    @Override
    public DocumentResponse getItemCostCollectionFile(UUID id) {
        ItemCostCollection itemCostCollection = itemCostCollectionService.findById(id);
        if (itemCostCollection == null) {
            throw new ImsBusinessException(ITEM_COST_COLLECTION_NOT_FOUND);
        }
        DocumentResponse response = new DocumentResponse();
        response.setName(itemCostCollection.getFileName());
        String url = documentApplicationService.getSignedUrl(itemCostCollection.getFileName());
        response.setSignedUrl(url);
        return response;
    }


    private String generateCollectionNumber(String vendorName, Integer retryTIme) {
        String collectionNumber = DateUtils.format(new Date(), DateUtils.DATE_FORMAT_14) + retryTIme;
        Integer key = collectionNumber.hashCode();
        EntityManager entityManager = managerFactory.createEntityManager();
        try {

            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager, key,
                "generateCollectionNumber ");
            if (isAcquired == null || !isAcquired) {
                log.warn(
                    "generateCollectionNumber collectionNumber is already in used, please try again later.");
                retryTIme++;
                if (retryTIme > 5) {
                    throw new ImsBusinessException(
                        "generateCollectionNumber collectionNumber error for retryTIme > 5 times. vendorName: {}", vendorName);
                }
                return generateCollectionNumber(vendorName, retryTIme);
            }
            return collectionNumber;

        } finally {
            pgAdvisoryLock.unLock(entityManager, key,
                "unlock for generateCollectionNumber");
            entityManager.close();
        }
    }

    private ItemCostCollectionDto createItemCostCollection(CreateItemCostCollectionCommand command) {
        ItemCostCollection itemCostCollection = ItemCostCollectionFactory.createItemCostCollection(
            command);
        itemCostCollection.setCollectionNumber(generateCollectionNumber(itemCostCollection.getVendorName(), 0));
        itemCostCollection = itemCostCollectionService.save(itemCostCollection);
        ItemCostCollectionDto itemCostCollectionDto = itemCostCollectionApplicationMapper.domainToDto(itemCostCollection);

        businessEventService.dispatch(ItemCostCollectionCreatedPayloadDto.builder()
            .itemCostCollectionId(itemCostCollectionDto.getId())
            .data(itemCostCollectionDto)
            .build());
        return itemCostCollectionDto;
    }


    @Override
    public void migrateFinale(int length) {
        FinaleOrderCollectionDto orderCollection = finaleExternalApiAdaptor.queryPurchaseOrdersByFilter(length);
        if (orderCollection == null || orderCollection.getOrderCount() == 0) {
            log.info("[migrateFinale] No purchase orders found from Finale");
            return;
        }

        orderCollection.getAllOrders().stream()
                .filter(this::isCompletedPurchaseOrder)
                .filter(this::isValidOrder)
                .filter(this::isNotJitPurchaseOrder)
                .forEach(this::processOrder);
    }

    private boolean isCompletedPurchaseOrder(FinaleOrderCollectionDto.OrderSummary order) {
        return order.isPurchaseOrder() && order.isCompleted();
    }

    private boolean isValidOrder(FinaleOrderCollectionDto.OrderSummary order) {
        return StringUtils.isNotBlank(order.getOrderId()) && StringUtils.isNotBlank(order.getSupplierId());
    }

    public boolean isNotJitPurchaseOrder(FinaleOrderCollectionDto.OrderSummary order) {
        String orderId = order.getOrderId();
        return StringUtils.isNotBlank(orderId) && !orderId.startsWith("J");
    }

    private void processOrder(FinaleOrderCollectionDto.OrderSummary order) {
        log.info("[SyncFinalePO] Processing order: {}", order.toString());
        createPOItemCostCollection(order);
    }

    private void createPOItemCostCollection(FinaleOrderCollectionDto.OrderSummary order) {
        String orderId = order.getOrderId();
        String vendorFinaleId = order.getSupplierId();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String lastUpdatedDate = order.getLastUpdatedDate() != null
                ? order.getLastUpdatedDate().format(formatter)
                : java.time.LocalDateTime.now().format(formatter);
        log.info(
                "[SyncFinalePO] Creating item cost collection for Finale PO orderId: {}, lastUpdatedDate:{}, vendorFinaleId: {}",
                orderId,
                lastUpdatedDate,
                vendorFinaleId);

        Vendor vendor = vendorService.findByFinaleId(vendorFinaleId);
        if (vendor == null) {
            log.warn("[SyncFinalePO] Vendor not found for finaleId: {}", vendorFinaleId);
            return;
        }

        String purchaseOrderNumber = orderId + "-" + lastUpdatedDate;

        List<ItemCostCollection> itemCostCollections = itemCostCollectionService.findByVendorId(vendor.getId());
        if (itemCostCollections.isEmpty() || itemCostCollections.stream()
                .noneMatch(itemCostCollection -> itemCostCollection.getVendorCollectionNumber()
                        .equals(purchaseOrderNumber))) {

            // Convert order to JSON string and upload to S3
            String orderJson = SerializationUtils.serialize(order);
            byte[] jsonBytes = orderJson.getBytes(StandardCharsets.UTF_8);
            String fileName = "finale-po-" + purchaseOrderNumber;

            DocumentResponse documentResponse = documentApplicationService.uploadFileContent(jsonBytes, fileName, Boolean.FALSE);
            log.info("[SyncFinalePO] Uploaded order JSON to S3: {}", documentResponse.getName());

            CreateItemCostCollectionCommand command = CreateItemCostCollectionCommand.builder()
                    .vendorId(vendor.getId())
                    .vendorName(vendor.getVendorName())
                    .source(ItemCostCollectionSources.FINALE_PURCHASE_ORDER)
                    .type(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER)
                    .vendorCollectionNumber(purchaseOrderNumber)
                    .fileName(documentResponse.getName())
                    .build();
            log.info("[SyncFinalePO] Creating item cost collection : {}", command);

            createItemCostCollection(command);

        }

    }

}
