package com.mercaso.ims.domain.itemvendorrebate;

import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.itemvendorrebate.enums.ItemVendorRebateStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * ItemVendorRebate domain entity
 * Represents rebate information for direct supplier items
 *
 * Business Rules:
 * - Start Date is required (when rebate becomes effective)
 * - End Date is optional (if null, rebate is continuous and valid indefinitely)
 * - Supplier must be a direct supplier
 * - SKU is required (item identification)
 * - Rebate Amount per Unit Sold is required (amount supplier refunds per sales unit)
 */
@Getter
@Setter
@ToString
@SuperBuilder
public class ItemVendorRebate extends BaseDomain {

    private final UUID id;

    private UUID vendorItemId;

    /** Direct supplier ID - must be a direct supplier */
    private UUID vendorId;

    /** Item ID associated with the SKU */
    private UUID itemId;

    /** Start Date - when the rebate becomes effective (required) */
    private LocalDate startDate;

    /** End Date - when rebate terminates (optional, null means continuous) */
    private LocalDate endDate;

    /** Rebate Amount per Unit Sold ($) - amount refunded per sales unit */
    private BigDecimal rebatePerUnit;

    private ItemVendorRebateStatus itemVendorRebateStatus;

}
