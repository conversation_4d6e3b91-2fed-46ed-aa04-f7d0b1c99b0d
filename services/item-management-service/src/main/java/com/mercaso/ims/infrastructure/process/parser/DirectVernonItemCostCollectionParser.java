package com.mercaso.ims.infrastructure.process.parser;

import static com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources.FINALE_PURCHASE_ORDER;
import static com.mercaso.ims.infrastructure.external.finale.FinalePackingParser.extractConversionFactor;

import com.mercaso.ims.application.dto.ItemCostCollectionItemParsingResultDto;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleOrderCollectionDto;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.mercaso.ims.infrastructure.util.SerializationUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class DirectVernonItemCostCollectionParser implements ItemCostCollectionParser {

    private final FinaleExternalApiAdaptor finaleExternalApiAdaptor;

    private final ItemCostCollectionService itemCostCollectionService;

    private final DocumentApplicationService documentApplicationService;

    private static final int COST_SCALE = 2;

    @Override
    public List<ItemCostCollectionItemParsingResultDto> parse(UUID itemCostCollectionId) {
        ItemCostCollection itemCostCollection = itemCostCollectionService.findById(itemCostCollectionId);

        if (null == itemCostCollection) {
            return new ArrayList<>();
        }
        if (!FINALE_PURCHASE_ORDER.equals(itemCostCollection.getSource())
            || !ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER.equals(itemCostCollection.getType())) {
            log.error("ItemCostCollection {} is not a valid source or type for DirectVernonItemCostCollectionParser",
                itemCostCollection);
            return new ArrayList<>();
        }

        FinaleOrderCollectionDto.OrderSummary orderSummary = getOrderSummary(itemCostCollection.getFileName());

        return orderSummary != null ? parseInvoiceItem(orderSummary) : List.of();

    }


    @Override
    public boolean isSupported(String vendorName, ItemCostCollectionSources sources) {
        return sources.equals(ItemCostCollectionSources.FINALE_PURCHASE_ORDER);
    }

    @Override
    public boolean isUpdateAvailability() {
        return false;
    }


    /**
     * Parses purchase order items and converts them to cost collection parsing results.
     * 
     * @param purchaseOrderDto the purchase order containing items to parse
     * @return list of parsing result DTOs
     */
    private List<ItemCostCollectionItemParsingResultDto> parseInvoiceItem(FinaleOrderCollectionDto.OrderSummary purchaseOrderDto) {
        return purchaseOrderDto.getOrderItemList().stream()
            .map(this::convertOrderItemToParsingResult)
            .toList();
    }

    /**
     * Converts a single order item to a parsing result DTO.
     * 
     * @param orderItem the order item to convert
     * @return the parsing result DTO
     */
    private ItemCostCollectionItemParsingResultDto convertOrderItemToParsingResult(FinaleOrderCollectionDto.OrderItem orderItem) {
        
        BigDecimal calculatedCost = calculateItemCost(orderItem);
        
        return ItemCostCollectionItemParsingResultDto.builder()
            .vendorSkuNumber(orderItem.getProductId())
            .cost(calculatedCost)
            .build();
    }

    /**
     * Calculates the cost for an order item based on conversion factor.
     * 
     * @param orderItem the order item
     * @return the calculated cost
     */
    private BigDecimal calculateItemCost(FinaleOrderCollectionDto.OrderItem orderItem) {

        if (null == orderItem.getUnitPrice()) {
            log.warn("Unit price is null for item {}", orderItem.getProductId());
            return null;
        }
        
        Double conversionFactor = extractConversionFactor(orderItem.getNormalizedPackingString());
        
        if (!isValidConversionFactor(conversionFactor)) {
            return orderItem.getUnitPrice();
        }

        try {
            return orderItem.getUnitPrice()
                .divide(BigDecimal.valueOf(conversionFactor), COST_SCALE, RoundingMode.HALF_UP);
        } catch (ArithmeticException e) {
            log.error("Division by zero error for item {} with conversion factor: {}",
                orderItem.getProductId(), conversionFactor, e);
            return orderItem.getUnitPrice();
        }
    }

    /**
     * Validates if the conversion factor is valid for calculation.
     * 
     * @param conversionFactor the conversion factor to validate
     * @return true if valid, false otherwise
     */
    private boolean isValidConversionFactor(Double conversionFactor) {
        return conversionFactor != null && conversionFactor > 0;
    }

    private FinaleOrderCollectionDto.OrderSummary getOrderSummary(String fileName) {
        byte[] bytes = documentApplicationService.downloadDocument(fileName);
        if (bytes == null) {
            log.error("File not found for fileName {}", fileName);
            return null;
        }

        try {
            // Convert bytes to string and parse as JSON
            String jsonContent = new String(bytes, StandardCharsets.UTF_8);
            log.info("Parsing order summary from file, jsonContent: {}", jsonContent);
            return SerializationUtils.deserialize(jsonContent, FinaleOrderCollectionDto.OrderSummary.class);
        } catch (Exception e) {
            log.error("Failed to parse order summary from file: {}", fileName, e);
            return null;
        }
    }


}