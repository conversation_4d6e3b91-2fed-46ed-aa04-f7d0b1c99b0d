package com.mercaso.ims.infrastructure.schedule;


import com.mercaso.document.operations.models.DocumentResponse;
import com.mercaso.ims.application.command.CreateItemCostCollectionCommand;
import com.mercaso.ims.application.service.DocumentApplicationService;
import com.mercaso.ims.application.service.ItemCostCollectionApplicationService;
import com.mercaso.ims.domain.itemcostcollection.ItemCostCollection;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionSources;
import com.mercaso.ims.domain.itemcostcollection.enums.ItemCostCollectionTypes;
import com.mercaso.ims.domain.itemcostcollection.service.ItemCostCollectionService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleOrderCollectionDto;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;

import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinalePurchaseOderScheduler {

    private static final Integer SYNC_FINALE_PURCHASE_ORDER_LOCK_KEY = "[FinalePurchaseOderScheduler.syncFinalePurchaseOrders]".hashCode();

    private final PgAdvisoryLock pgAdvisoryLock;
    private final VendorService vendorService;
    private final ItemCostCollectionApplicationService itemCostCollectionApplicationService;
    private final FinaleExternalApiAdaptor finaleExternalApiAdaptor;
    private final ItemCostCollectionService itemCostCollectionService;
    private final EntityManagerFactory managerFactory;
    private final DocumentApplicationService documentApplicationService;

    @Scheduled(cron = "0 0 * * * *")
    public void syncFinalePurchaseOrders() {
        log.info(" Starting [SyncFinalePO] Task");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            if (!tryAcquireLock(entityManager)) {
                log.warn("[SyncFinalePO] Already in progress, exiting...");
                return;
            }

            FinaleOrderCollectionDto orderCollection = finaleExternalApiAdaptor.queryPurchaseOrdersByFilter(3);
            if (orderCollection == null || orderCollection.getOrderCount() == 0) {
                log.info("[SyncFinalePO] No purchase orders found from Finale");
                return;
            }

            orderCollection.getAllOrders().stream()
                    .filter(this::isCompletedPurchaseOrder)
                    .filter(this::isValidOrder)
                    .filter(this::isNotJitPurchaseOrder)
                    .forEach(this::processOrder);

        } catch (Exception e) {
            log.error("[SyncFinalePO] Exception occurred: {}", e.getMessage(), e);
        } finally {
            releaseLock(entityManager);
            entityManager.close();
            log.info("Finished [SyncFinalePO] Task ");
        }

    }

    private boolean tryAcquireLock(EntityManager entityManager) {
        return Boolean.TRUE.equals(pgAdvisoryLock.tryLockWithSessionLevel(
            entityManager, SYNC_FINALE_PURCHASE_ORDER_LOCK_KEY, "SyncFinalePO"));
    }

    private void releaseLock(EntityManager entityManager) {
        pgAdvisoryLock.unLock(entityManager, SYNC_FINALE_PURCHASE_ORDER_LOCK_KEY, "Unlock SyncFinalePO");
    }

    private void createPOItemCostCollection(FinaleOrderCollectionDto.OrderSummary order) {
        String orderId = order.getOrderId();
        String vendorFinaleId = order.getSupplierId();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String lastUpdatedDate = order.getLastUpdatedDate() != null 
            ? order.getLastUpdatedDate().format(formatter)
            : java.time.LocalDateTime.now().format(formatter);
        log.info(
            "[SyncFinalePO] Creating item cost collection for Finale PO orderId: {}, lastUpdatedDate:{}, vendorFinaleId: {}",
            orderId,
            lastUpdatedDate,
            vendorFinaleId);

        Vendor vendor = vendorService.findByFinaleId(vendorFinaleId);
        if (vendor == null) {
            log.warn("[SyncFinalePO] Vendor not found for finaleId: {}", vendorFinaleId);
            return;
        }

        String purchaseOrderNumber = orderId + "-" + lastUpdatedDate;

        List<ItemCostCollection> itemCostCollections = itemCostCollectionService.findByVendorId(vendor.getId());
        if (itemCostCollections.isEmpty() || itemCostCollections.stream()
            .noneMatch(itemCostCollection -> itemCostCollection.getVendorCollectionNumber()
                .equals(purchaseOrderNumber))) {

            // Convert order to JSON string and upload to S3
            String orderJson = SerializationUtils.serialize(order);
            byte[] jsonBytes = orderJson.getBytes(StandardCharsets.UTF_8);
            String fileName = "finale-po-" + purchaseOrderNumber;

            DocumentResponse documentResponse = documentApplicationService.uploadFileContent(jsonBytes, fileName, Boolean.FALSE);
            log.info("[SyncFinalePO] Uploaded order JSON to S3: {}", documentResponse.getName());

            CreateItemCostCollectionCommand command = CreateItemCostCollectionCommand.builder()
                .vendorId(vendor.getId())
                .vendorName(vendor.getVendorName())
                .source(ItemCostCollectionSources.FINALE_PURCHASE_ORDER)
                .type(ItemCostCollectionTypes.ON_LINE_PURCHASE_ORDER)
                .vendorCollectionNumber(purchaseOrderNumber)
                .fileName(documentResponse.getName())
                .build();
            log.info("[SyncFinalePO] Creating item cost collection : {}", command);

            itemCostCollectionApplicationService.create(command);
        }

    }

    private boolean isCompletedPurchaseOrder(FinaleOrderCollectionDto.OrderSummary order) {
        return order.isPurchaseOrder() && order.isCompleted();
    }

    private boolean isValidOrder(FinaleOrderCollectionDto.OrderSummary order) {
        return StringUtils.isNotBlank(order.getOrderId()) && StringUtils.isNotBlank(order.getSupplierId());
    }

    private void processOrder(FinaleOrderCollectionDto.OrderSummary order) {
        log.info("[SyncFinalePO] Processing order: {}", order.toString());
        createPOItemCostCollection(order);
    }

    public boolean isNotJitPurchaseOrder(FinaleOrderCollectionDto.OrderSummary order) {
        String orderId = order.getOrderId();
        return StringUtils.isNotBlank(orderId) && !orderId.startsWith("J");
    }
}