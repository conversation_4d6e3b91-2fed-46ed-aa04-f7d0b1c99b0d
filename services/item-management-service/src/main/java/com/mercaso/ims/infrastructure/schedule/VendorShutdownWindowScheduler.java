package com.mercaso.ims.infrastructure.schedule;

import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

import com.mercaso.ims.application.command.UpdateVendorItemCommand;
import com.mercaso.ims.application.service.VendorItemApplicationService;
import com.mercaso.ims.domain.vendor.Vendor;
import com.mercaso.ims.domain.vendor.service.VendorService;
import com.mercaso.ims.domain.vendoritem.VendorItem;
import com.mercaso.ims.domain.vendoritem.VendorItemAvailabilitySnapshotDetail;
import com.mercaso.ims.domain.vendoritem.service.VendorItemAvailabilitySnapshotService;
import com.mercaso.ims.domain.vendoritem.service.VendorItemService;
import com.mercaso.ims.infrastructure.config.PgAdvisoryLock;
import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityManagerFactory;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class VendorShutdownWindowScheduler {

    private static final Integer SHUTDOWN_WINDOW_LOCK_KEY = "[VendorShutdownWindowScheduler]".hashCode();

    private final PgAdvisoryLock pgAdvisoryLock;
    private final EntityManagerFactory managerFactory;
    private final VendorService vendorService;
    private final VendorItemService vendorItemService;
    private final VendorItemApplicationService vendorItemApplicationService;
    private final VendorItemAvailabilitySnapshotService snapshotService;


    //    @Scheduled(cron = "0 0 5 * * FRI", zone = "America/Los_Angeles")
    @Scheduled(cron = "0 0 12 * * THU", zone = "America/Los_Angeles")
    public void shutdownVendorsOnFriday() {
        log.info("Starting vendor shutdown window task - Friday 05:00");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                SHUTDOWN_WINDOW_LOCK_KEY,
                "Vendor Shutdown Window Task - Friday");
            if (isAcquired == null || !isAcquired) {
                log.warn("[VendorShutdownWindowScheduler.shutdownVendorsOnFriday] is already in progress, please try again later.");
                return;
            }

            processVendorShutdown(false);

        } catch (Exception e) {
            log.error("[shutdownVendorsOnFriday] Exception occurred: {}", e.getMessage(), e);
        } finally {
            pgAdvisoryLock.unLock(entityManager,
                SHUTDOWN_WINDOW_LOCK_KEY,
                "unlock [VendorShutdownWindowScheduler.shutdownVendorsOnFriday]");
            entityManager.close();
            log.info("Finished vendor shutdown window task - Friday 05:00");
        }
    }


    //    @Scheduled(cron = "0 0 6 * * SAT", zone = "America/Los_Angeles")
    @Scheduled(cron = "0 0 17 * * THU", zone = "America/Los_Angeles")
    public void restoreVendorsOnSaturday() {
        log.info("Starting vendor restore window task - Saturday 06:00");
        EntityManager entityManager = managerFactory.createEntityManager();
        try {
            Boolean isAcquired = pgAdvisoryLock.tryLockWithSessionLevel(entityManager,
                SHUTDOWN_WINDOW_LOCK_KEY,
                "Vendor Restore Window Task - Saturday");
            if (isAcquired == null || !isAcquired) {
                log.warn(
                    "[VendorShutdownWindowScheduler.restoreVendorsOnSaturday] is already in progress, please try again later.");
                return;
            }

            processVendorShutdown(true);

        } catch (Exception e) {
            log.error("[restoreVendorsOnSaturday] Exception occurred: {}", e.getMessage(), e);
        } finally {
            pgAdvisoryLock.unLock(entityManager,
                SHUTDOWN_WINDOW_LOCK_KEY,
                "unlock [VendorShutdownWindowScheduler.restoreVendorsOnSaturday]");
            entityManager.close();
            log.info("Finished vendor restore window task - Saturday 06:00");
        }
    }


    private void processVendorShutdown(boolean restore) {
        String action = restore ? "restore" : "shutdown";
        log.info("Processing vendor {} window for enabled vendors", action);

        List<Vendor> vendors = vendorService.findByShutdownWindowEnabled();
        log.info("Found {} vendors with shutdown window enabled", vendors.size());

        for (Vendor vendor : vendors) {
            try {
                if (vendor.isWithinShutdownWindow() != restore) {
                    log.info("Vendor {} is not within shutdown window, skipping", vendor.getVendorName());
                    processVendorItems(vendor, restore);
                }
            } catch (Exception e) {
                log.error("Error processing vendor {} for {}: {}",
                    vendor.getVendorName(), action, e.getMessage(), e);
            }
        }
    }


    private void processVendorItems(Vendor vendor, boolean restore) {
        String action = restore ? "restore" : "shutdown";
        log.info("Processing vendor items for vendor: {} - action: {}", vendor.getVendorName(), action);

        List<VendorItem> vendorItems = vendorItemService.findByVendorID(vendor.getId());
        log.info("Found {} vendor items for vendor: {}", vendorItems.size(), vendor.getVendorName());

        if (restore) {
            processVendorItemsRestore(vendor, vendorItems);
        } else {
            processVendorItemsShutdown(vendor, vendorItems);
        }
    }


    private void processVendorItemsShutdown(Vendor vendor, List<VendorItem> vendorItems) {
        log.info("Processing shutdown for vendor: {}", vendor.getVendorName());

        List<VendorItemAvailabilitySnapshotDetail> snapshotDetails = vendorItems.stream()
            .map(vendorItem -> VendorItemAvailabilitySnapshotDetail.builder()
                .vendorItemId(vendorItem.getId())
                .vendorSkuNumber(vendorItem.getVendorSkuNumber())
                .itemId(vendorItem.getItemId())
                .previousAvailability(vendorItem.getAvailability())
                .newAvailability(false)
                .build())
            .collect(Collectors.toList());

        snapshotService.createShutdownSnapshot(vendor.getId(), snapshotDetails);

        int processedCount = 0;
        int successCount = 0;

        for (VendorItem vendorItem : vendorItems) {
            try {
                updateVendorItemAvailability(vendorItem, false, vendor);
                successCount++;
                processedCount++;
            } catch (Exception e) {
                log.error("Error processing vendor item {} for vendor {}: {}",
                    vendorItem.getVendorSkuNumber(), vendor.getVendorName(), e.getMessage(), e);
                processedCount++;
            }
        }

        log.info("Processed {} vendor items for vendor: {} - {} successful",
            processedCount, vendor.getVendorName(), successCount);
    }


    private void processVendorItemsRestore(Vendor vendor, List<VendorItem> vendorItems) {
        log.info("Processing restore for vendor: {}", vendor.getVendorName());

        List<VendorItemAvailabilitySnapshotDetail> shutdownDetails = snapshotService.getLatestShutdownSnapshotDetails(vendor.getId());

        if (shutdownDetails.isEmpty()) {
            log.error("No shutdown snapshot found for vendor: {}, restoring all items to available", vendor.getVendorName());
            processVendorItemsRestoreAll(vendor, vendorItems);
            return;
        }

        List<VendorItemAvailabilitySnapshotDetail> restoreDetails = vendorItems.stream()
            .map(vendorItem -> {
                VendorItemAvailabilitySnapshotDetail shutdownDetail = shutdownDetails.stream()
                    .filter(detail -> detail.getVendorItemId().equals(vendorItem.getId()))
                    .findFirst()
                    .orElse(null);

                Boolean previousAvailability = shutdownDetail != null ? shutdownDetail.getPreviousAvailability() : true;

                return VendorItemAvailabilitySnapshotDetail.builder()
                    .vendorItemId(vendorItem.getId())
                    .vendorSkuNumber(vendorItem.getVendorSkuNumber())
                    .itemId(vendorItem.getItemId())
                    .previousAvailability(vendorItem.getAvailability())
                    .newAvailability(previousAvailability)
                    .build();
            })
            .collect(Collectors.toList());

        snapshotService.createRestoreSnapshot(vendor.getId(), restoreDetails);

        int processedCount = 0;
        int successCount = 0;

        for (VendorItem vendorItem : vendorItems) {
            try {
                VendorItemAvailabilitySnapshotDetail shutdownDetail = shutdownDetails.stream()
                    .filter(detail -> detail.getVendorItemId().equals(vendorItem.getId()))
                    .findFirst()
                    .orElse(null);

                Boolean restoreAvailability = shutdownDetail != null ? shutdownDetail.getPreviousAvailability() : true;
                updateVendorItemAvailabilityToSpecific(vendorItem, restoreAvailability, vendor);
                successCount++;
                processedCount++;
            } catch (Exception e) {
                log.error("Error processing vendor item {} for vendor {}: {}",
                    vendorItem.getVendorSkuNumber(), vendor.getVendorName(), e.getMessage(), e);
                processedCount++;
            }
        }

        log.info("Processed {} vendor items for vendor: {} - {} successful",
            processedCount, vendor.getVendorName(), successCount);
    }


    private void processVendorItemsRestoreAll(Vendor vendor, List<VendorItem> vendorItems) {
        log.info("Processing restore all for vendor: {}", vendor.getVendorName());

        List<VendorItemAvailabilitySnapshotDetail> restoreDetails = vendorItems.stream()
            .map(vendorItem -> VendorItemAvailabilitySnapshotDetail.builder()
                .vendorItemId(vendorItem.getId())
                .vendorSkuNumber(vendorItem.getVendorSkuNumber())
                .itemId(vendorItem.getItemId())
                .previousAvailability(vendorItem.getAvailability())
                .newAvailability(true)
                .build())
            .collect(Collectors.toList());

        snapshotService.createRestoreSnapshot(vendor.getId(), restoreDetails);

        int processedCount = 0;
        int successCount = 0;

        for (VendorItem vendorItem : vendorItems) {
            try {
                updateVendorItemAvailability(vendorItem, true, vendor);
                successCount++;
                processedCount++;
            } catch (Exception e) {
                log.error("Error processing vendor item {} for vendor {}: {}",
                    vendorItem.getVendorSkuNumber(), vendor.getVendorName(), e.getMessage(), e);
                processedCount++;
            }
        }

        log.info("Processed {} vendor items for vendor: {} - {} successful",
            processedCount, vendor.getVendorName(), successCount);
    }


    private boolean isWithinShutdownWindow(Vendor vendor) {
        return vendor.isWithinShutdownWindow();
    }


    private void updateVendorItemAvailability(VendorItem vendorItem, boolean restore, Vendor vendor) {
        String action = restore ? "restore" : "shutdown";
        Boolean newAvailability = restore;

        log.info("Updating vendor item availability for {} - vendor: {}, item: {}, new availability: {}",
            action, vendor.getVendorName(), vendorItem.getVendorSkuNumber(), newAvailability);

        UpdateVendorItemCommand updateCommand = UpdateVendorItemCommand.builder()
            .vendorItemId(vendorItem.getId())
            .vendorSkuNumber(vendorItem.getVendorSkuNumber())
            .vendorItemName(vendorItem.getVendorItemName())
            .cost(vendorItem.getCost())
            .backupCost(vendorItem.getBackupPackPlusCrvCost())
            .note(vendorItem.getNote())
            .aisle(vendorItem.getAisle())
            .isCostRefreshed(false)
            .isBackupCostRefreshed(false)
            .availability(newAvailability)
            .build();

        vendorItemApplicationService.update(updateCommand);

        log.info("Successfully updated vendor item availability for {} - vendor: {}, item: {}",
            action, vendor.getVendorName(), vendorItem.getVendorSkuNumber());
    }


    private void updateVendorItemAvailabilityToSpecific(VendorItem vendorItem, Boolean newAvailability, Vendor vendor) {
        log.info("Updating vendor item availability to specific - vendor: {}, item: {}, new availability: {}",
            vendor.getVendorName(), vendorItem.getVendorSkuNumber(), newAvailability);

        UpdateVendorItemCommand updateCommand = UpdateVendorItemCommand.builder()
            .vendorItemId(vendorItem.getId())
            .vendorSkuNumber(vendorItem.getVendorSkuNumber())
            .vendorItemName(vendorItem.getVendorItemName())
            .cost(vendorItem.getCost())
            .backupCost(vendorItem.getBackupPackPlusCrvCost())
            .note(vendorItem.getNote())
            .aisle(vendorItem.getAisle())
            .isCostRefreshed(false)
            .isBackupCostRefreshed(false)
            .availability(newAvailability)
            .build();

        vendorItemApplicationService.update(updateCommand);

        log.info("Successfully updated vendor item availability to specific - vendor: {}, item: {}",
            vendor.getVendorName(), vendorItem.getVendorSkuNumber());
    }

    @Transactional(propagation = REQUIRES_NEW)
    public void manualShutdown(UUID vendorId) {
        log.info("Manual shutdown triggered from vendor: {}", vendorId);
        Vendor vendor = vendorService.findById(vendorId);
        if (vendor == null) {
            log.error("Vendor not found for manual shutdown with ID: {}", vendorId);
            return;
        }

        if (!isManualOperationAllowed(vendor, "shutdown")) {
            log.warn("Manual shutdown not allowed for vendor: {} - outside shutdown window", vendor.getVendorName());
            return;
        }

        processVendorItems(vendor, false);
    }

    @Transactional(propagation = REQUIRES_NEW)
    public void manualRestore(UUID vendorId) {
        log.info("Manual restore triggered from vendor: {}", vendorId);
        Vendor vendor = vendorService.findById(vendorId);
        if (vendor == null) {
            log.error("Vendor not found for manual restore with ID: {}", vendorId);
            return;
        }

        if (!isManualOperationAllowed(vendor, "restore")) {
            log.warn("Manual restore not allowed for vendor: {} - outside shutdown window", vendor.getVendorName());
            return;
        }

        processVendorItems(vendor, true);
    }


    /**
     * Check if manual operations are allowed for a vendor based on their shutdown window configuration. This method can be used
     * to enforce vendor-specific time restrictions for manual operations.
     *
     * @param vendor The vendor to check
     * @param operation The operation being performed ("shutdown" or "restore")
     * @return true if the operation is allowed, false otherwise
     */
    private boolean isManualOperationAllowed(Vendor vendor, String operation) {
        // If vendor doesn't have shutdown window enabled, allow all manual operations
        if (!Boolean.TRUE.equals(vendor.getShutdownWindowEnabled())) {
            log.debug("Manual {} allowed for vendor: {} - shutdown window not enabled",
                operation, vendor.getVendorName());
            return true;
        }

        // Check if current time is within the vendor's shutdown window
        boolean withinWindow = isWithinShutdownWindow(vendor);
        if (withinWindow) {
            log.debug("Manual {} allowed for vendor: {} - within shutdown window",
                operation, vendor.getVendorName());
        } else {
            log.debug("Manual {} not allowed for vendor: {} - outside shutdown window",
                operation, vendor.getVendorName());
        }

        return withinWindow;
    }
} 