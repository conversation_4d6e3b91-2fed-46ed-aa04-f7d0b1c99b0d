package com.mercaso.ims.domain.itemvendorrebate;

import com.mercaso.ims.domain.itemvendorrebate.enums.ItemVendorRebateStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Factory for creating ItemVendorRebate domain entities
 *
 * Business Rules:
 * - Start Date: Required - when rebate becomes effective
 * - End Date: Optional - if null, rebate is continuous and valid indefinitely
 * - Supplier: Required - must be a direct supplier
 * - SKU: Required - item identification
 * - Rebate Amount per Unit Sold: Required - must be positive amount
 */
@Slf4j
@Component
public class ItemVendorRebateFactory {

    /**
     * Create a new ItemVendorRebate entity
     * @param vendorItemId the vendor item ID
     * @param vendorId the vendor ID
     * @param itemId the item ID
     * @param startDate the start date of the rebate
     * @param endDate the end date of the rebate (can be null for indefinite)
     * @param rebatePerUnit the rebate amount per unit
     * @return the created ItemVendorRebate entity
     */
    public ItemVendorRebate create(UUID vendorItemId, UUID vendorId, UUID itemId, 
                                   LocalDate startDate, LocalDate endDate, BigDecimal rebatePerUnit) {
        log.debug("Creating ItemVendorRebate for vendorItemId: {}, vendorId: {}, itemId: {}", 
                  vendorItemId, vendorId, itemId);
        
        validateInputs(vendorItemId, vendorId, itemId, startDate, rebatePerUnit);
        
        return ItemVendorRebate.builder()
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(startDate)
                .endDate(endDate)
                .rebatePerUnit(rebatePerUnit)
                .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
                .build();
    }

    /**
     * Validate the input parameters for creating an ItemVendorRebate according to business rules
     * @param vendorItemId the vendor item ID
     * @param vendorId the vendor ID (direct supplier)
     * @param itemId the item ID (SKU)
     * @param startDate the start date (required)
     * @param rebatePerUnit the rebate per unit (must be positive)
     */
    private void validateInputs(UUID vendorItemId, UUID vendorId, UUID itemId,
                                LocalDate startDate, BigDecimal rebatePerUnit) {
        if (vendorItemId == null) {
            throw new IllegalArgumentException("Vendor item ID cannot be null");
        }
        if (vendorId == null) {
            throw new IllegalArgumentException("Supplier ID cannot be null - must be a direct supplier");
        }
        if (itemId == null) {
            throw new IllegalArgumentException("Item ID (SKU) cannot be null");
        }
        if (startDate == null) {
            throw new IllegalArgumentException("Start Date cannot be null - when rebate becomes effective");
        }
        if (rebatePerUnit == null) {
            throw new IllegalArgumentException("Rebate Amount per Unit Sold cannot be null");
        }
        if (rebatePerUnit.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Rebate Amount per Unit Sold must be greater than zero");
        }
    }
}
