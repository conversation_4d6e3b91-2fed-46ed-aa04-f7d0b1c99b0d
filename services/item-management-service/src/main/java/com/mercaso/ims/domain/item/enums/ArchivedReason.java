package com.mercaso.ims.domain.item.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.Arrays;

public enum ArchivedReason {
    DUPLICATE,
    DISCONTINUED,
    HUMAN_ERROR,
    UNKNOWN,

    ;

    @JsonCreator
    public static ArchivedReason fromString(String name) {
        return Arrays.stream(values()).filter(v -> v.name().equalsIgnoreCase(name)).findFirst().orElse(UNKNOWN);
    }
}
