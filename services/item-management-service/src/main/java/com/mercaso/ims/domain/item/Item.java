package com.mercaso.ims.domain.item;

import com.mercaso.ims.application.command.UpdateItemCommand;
import com.mercaso.ims.domain.AggregateRoot;
import com.mercaso.ims.domain.BaseDomain;
import com.mercaso.ims.domain.attribute.AttributeConstant;
import com.mercaso.ims.domain.item.enums.*;
import com.mercaso.ims.domain.itemattribute.ItemAttribute;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@Getter
@Setter
@ToString
@SuperBuilder
public class Item extends BaseDomain implements AggregateRoot {

    private final UUID id;

    private UUID categoryId;

    private UUID brandId;

    private String name;

    private String title;

    private String skuNumber;

    private String description;

    private String note;

    private String photo;

    private UUID primaryVendorId;

    private UUID backupVendorId;

    private String detail;

    private PackageType packageType;

    private Integer packageSize;

    private String shelfLife;

    private ItemType itemType;

    private SalesStatus salesStatus;

    private AvailabilityStatus availabilityStatus;

    private Long companyId;

    private Long locationId;

    private String handle;

    private String department;

    private String category;

    private String subCategory;

    private String clazz;

    private String newDescription;

    private Double length;

    private Double height;

    private Double width;

    private List<ItemAttribute> itemAttributes;

    private List<ItemTag> itemTags;

    private List<ItemImage> itemImages;

    private List<ItemUPC> itemUPCs;

    private ItemParetoGrade itemGrade;

    private MissingEachUpcReason missingEachUpcReason;

    private MissingCaseUpcReason missingCaseUpcReason;

    private ArchivedReason archivedReason;


    public void updateItemUPCs(String itemUPCs, ItemUpcType upcType) {
        if (StringUtils.isNotBlank(itemUPCs)) {
            cleanItemUPCs();
            List<ItemUPC> itemUpcList = new ArrayList<>();
            String[] upcArray = itemUPCs.split(",");
            for (String upc : upcArray) {
                if (StringUtils.isNotBlank(upc)) {
                    upc = upc.trim();
                    itemUpcList.add(ItemUPC.builder()
                        .upcNumber(upc)
                        .itemUpcType(upcType)
                        .build());
                }
            }
            this.itemUPCs.addAll(itemUpcList);
        }
    }

    public void updateItemUPCs(List<ItemUPC> itemUpcList) {
        if (itemUpcList != null) {
            cleanItemUPCs();
            this.itemUPCs.addAll(itemUpcList);
        }
    }

    public void cleanItemUPCs() {
        if (this.itemUPCs != null) {
            this.itemUPCs.clear();
        } else {
            this.itemUPCs = new ArrayList<>();
        }
    }

    public void updateItemAttributes(List<ItemAttribute> itemAttributes) {
        if (itemAttributes != null) {
            if (this.itemAttributes != null) {
                this.itemAttributes.clear();
            } else {
                this.itemAttributes = new ArrayList<>();
            }
            itemAttributes.stream().map(itemAttribute -> {
                itemAttribute.setItemId(id);
                return itemAttribute;
            }).forEach(this.itemAttributes::add);
        }
    }


    public Item update(UpdateItemCommand updateItemCommand) {

        setIfNotNull(() -> this.categoryId = updateItemCommand.getCategoryId(), updateItemCommand.getCategoryId());
        setIfNotNull(() -> this.brandId = updateItemCommand.getBrandId(), updateItemCommand.getBrandId());
        setIfNotNull(() -> this.name = updateItemCommand.getTitle(), updateItemCommand.getTitle());
        setIfNotNull(() -> this.title = updateItemCommand.getTitle(), updateItemCommand.getTitle());
        setIfNotNull(() -> this.skuNumber = updateItemCommand.getSkuNumber(), updateItemCommand.getSkuNumber());
        setIfNotNull(() -> this.description = updateItemCommand.getDescription(), updateItemCommand.getDescription());
        setIfNotNull(() -> this.note = updateItemCommand.getNote(), updateItemCommand.getNote());
        setIfNotNull(() -> this.primaryVendorId = updateItemCommand.getPrimaryVendorId(), updateItemCommand.getPrimaryVendorId());
        setIfNotNull(() -> this.detail = updateItemCommand.getDetail(), updateItemCommand.getDetail());
        setIfNotNull(() -> this.packageType = updateItemCommand.getPackageType(), updateItemCommand.getPackageType());
        setIfNotNull(() -> this.packageSize = updateItemCommand.getPackageSize(), updateItemCommand.getPackageSize());
        setIfNotNull(() -> this.shelfLife = updateItemCommand.getShelfLife(), updateItemCommand.getShelfLife());
        setIfNotNull(() -> this.itemType = updateItemCommand.getItemType(), updateItemCommand.getItemType());
        setIfNotNull(() -> this.salesStatus = updateItemCommand.getSalesStatus(), updateItemCommand.getSalesStatus());
        setIfNotNull(() -> this.availabilityStatus = updateItemCommand.getAvailabilityStatus(),
            updateItemCommand.getAvailabilityStatus());
        setIfNotNull(() -> this.companyId = updateItemCommand.getCompanyId(), updateItemCommand.getCompanyId());
        setIfNotNull(() -> this.locationId = updateItemCommand.getLocationId(), updateItemCommand.getLocationId());
        setIfNotNull(() -> this.department = updateItemCommand.getDepartment(), updateItemCommand.getDepartment());
        setIfNotNull(() -> this.category = updateItemCommand.getCategory(), updateItemCommand.getCategory());
        setIfNotNull(() -> this.subCategory = updateItemCommand.getSubCategory(), updateItemCommand.getSubCategory());
        setIfNotNull(() -> this.clazz = updateItemCommand.getClazz(), updateItemCommand.getClazz());
        setIfNotNull(() -> this.newDescription = updateItemCommand.getNewDescription(), updateItemCommand.getNewDescription());
        setIfNotNull(() -> this.width = updateItemCommand.getWidth(), updateItemCommand.getWidth());
        setIfNotNull(() -> this.length = updateItemCommand.getLength(), updateItemCommand.getLength());
        setIfNotNull(() -> this.height = updateItemCommand.getHeight(), updateItemCommand.getHeight());
        setIfNotNull(() -> this.backupVendorId = updateItemCommand.getBackupVendorId(), updateItemCommand.getBackupVendorId());
        setIfNotNull(() -> this.archivedReason = updateItemCommand.getArchivedReason(), updateItemCommand.getArchivedReason());
        setMissingUpcReason(updateItemCommand.getMissingEachUpcReason(), updateItemCommand.getMissingCaseUpcReason());
        return this;
    }

    private <T> void setIfNotNull(Runnable setter, T value) {
        if (value != null) {
            setter.run();
        }
    }

    public void setItemDefaultTags(String brandName) {
        if (this.itemTags == null) {
            this.itemTags = new ArrayList<>();
        }

        Set<String> existingTagNames = this.itemTags.stream()
            .map(ItemTag::getTagName)
            .collect(Collectors.toSet());

        addTagIfNotExists(existingTagNames, department);

        addTagIfNotExists(existingTagNames, category);

        addTagIfNotExists(existingTagNames, subCategory);

        addTagIfNotExists(existingTagNames, clazz);

        if (StringUtils.isNotBlank(brandName)) {
            addTagIfNotExists(existingTagNames, "Brand_" + brandName);
        }

        addTagIfNotExists(existingTagNames, "UPP:" + packageSize);
    }

    public void addTagsIfNotExists(List<String> tagNames) {
        if (CollectionUtils.isNotEmpty(tagNames)) {
            Set<String> existingTagNames = this.itemTags.stream()
                .map(ItemTag::getTagName)
                .collect(Collectors.toSet());
            tagNames.forEach(tagName -> addTagIfNotExists(existingTagNames, tagName));
        }
    }

    private void addTagIfNotExists(Set<String> existingTagNames, String tagName) {
        if (StringUtils.isNotBlank(tagName) && !existingTagNames.contains(tagName)) {
            this.itemTags.add(ItemTag.builder()
                .itemId(id)
                .tagName(tagName)
                .build());
            existingTagNames.add(tagName);
        }
    }

    public void updateItemTags(List<String> newTags) {
        if (newTags == null) {
            return;
        }
        if (this.itemTags != null) {
            this.itemTags.clear();
        } else {
            this.itemTags = new ArrayList<>();
        }

        newTags.stream()
            .filter(StringUtils::isNotBlank)
            .distinct()
            .map(tag -> ItemTag.builder()
                .itemId(id)
                .tagName(tag)
                .build())
            .forEach(this.itemTags::add);
    }

    public Item active() {
        this.availabilityStatus = AvailabilityStatus.ACTIVE;
        return this;
    }

    public Item draft() {
        this.availabilityStatus = AvailabilityStatus.DRAFT;
        return this;
    }

    public Item archive(ArchivedReason archivedReason) {
        this.availabilityStatus = AvailabilityStatus.ARCHIVED;
        this.archivedReason = archivedReason;
        return this;
    }

    public void updateItemImages(List<ItemImage> newItemImages) {
        if (CollectionUtils.isEmpty(newItemImages)) {
            return;
        }
        this.itemImages = new ArrayList<>();

        for (ItemImage image : newItemImages) {
            log.info("Updating image: {}, image type :{}", image.getFileName(), image.getImageType());
            if (image.getImageType().equals(ImageType.MAIN_IMAGE)) {
                this.photo = image.getFileName();
            }
            image.setItemId(this.id);
            this.itemImages.add(image);
        }
    }

    public ItemAttribute getItemBottleSizeAttribute() {
        return itemAttributes == null ? null : itemAttributes.stream()
            .filter(attr -> attr.getAttributeId().equals(AttributeConstant.BOTTLE_SIZE_ATTRIBUTE_ID))
            .findFirst()
            .orElseGet(() -> itemAttributes.stream()
                .filter(attr -> attr.getAttributeId().equals(AttributeConstant.ITEM_SIZE_ATTRIBUTE_ID))
                .findFirst()
                .orElse(null));
    }

    public void setMissingUpcReason(MissingEachUpcReason newEachReason, MissingCaseUpcReason newCaseReason) {
        if (itemUPCs == null) {
            itemUPCs = new ArrayList<>();
        }

        Set<ItemUpcType> presentUpcTypes = itemUPCs.stream()
                .map(ItemUPC::getItemUpcType)
                .collect(Collectors.toSet());

        this.missingEachUpcReason = determineNewReason(
                presentUpcTypes,
                ItemUpcType.EACH_UPC,
                this.missingEachUpcReason,
                newEachReason,
                MissingEachUpcReason.UNKNOWN
        );

        this.missingCaseUpcReason = determineNewReason(
                presentUpcTypes,
                ItemUpcType.CASE_UPC,
                this.missingCaseUpcReason,
                newCaseReason,
                MissingCaseUpcReason.UNKNOWN
        );
    }


    private <T> T determineNewReason(Set<ItemUpcType> presentTypes,
                                     ItemUpcType typeToCheck,
                                     T currentReason,
                                     T newReason,
                                     T defaultReason) {
        if (presentTypes.contains(typeToCheck)) {
            return null;
        }

        return ObjectUtils.firstNonNull(newReason, currentReason, defaultReason);
    }
}
