package com.mercaso.ims.application.service.impl;

import static org.springframework.transaction.annotation.Propagation.REQUIRES_NEW;

import com.mercaso.ims.application.dto.ItemDto;
import com.mercaso.ims.application.service.FinaleApplicationService;
import com.mercaso.ims.infrastructure.external.finale.FinaleExternalApiAdaptor;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleProductDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorDto;
import com.mercaso.ims.infrastructure.external.finale.dto.FinaleVendorInfoDto;
import com.mercaso.ims.infrastructure.external.finale.enums.StatusId;
import java.math.BigDecimal;
import java.util.Optional;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
@RequiredArgsConstructor
public class FinaleApplicationServiceImpl implements FinaleApplicationService {

    private final FinaleExternalApiAdaptor finaleExternalApiAdaptor;
    private final Environment environment;

    @Async
    @Override
    @Transactional(propagation = REQUIRES_NEW)
    public void syncItemToFinale(ItemDto item) {
        if (environment.getActiveProfiles().length > 0 && environment.getActiveProfiles()[0].equals("sat")) {
            log.info("Skip sync item to finale for [SAT]");
            return;
        }
        log.info("Sync item to finale for item={}", item);
        StatusId statusId = StatusId.fromAvailabilityStatus(item.getAvailabilityStatus());

        FinaleProductDto finaleProductDto = finaleExternalApiAdaptor.getFinaleProduct(item.getSkuNumber(), statusId);

        syncStatus(item.getSkuNumber(), finaleProductDto, statusId);
        syncVendorItem(item, finaleProductDto, statusId);

    }

    @Override
    public FinaleVendorDto createVendor(String vendorName) {
        if (environment.getActiveProfiles().length > 0 && environment.getActiveProfiles()[0].equals("sat")) {
            log.info("[createVendor] DO nothing as create Finale supplier for [SAT]");
            return FinaleVendorDto.builder().partyId("1").build();
        }
        return finaleExternalApiAdaptor.createVendor(vendorName);
    }

    @Override
    public FinaleVendorDto getVendor(String vendorName) {
        return finaleExternalApiAdaptor.getVendor(vendorName);
    }

    @Override
    public FinaleVendorInfoDto getVendorById(String finaleId) {
        log.info("getVendorById finaleId: {}", finaleId);
        return finaleExternalApiAdaptor.getVendorById(finaleId);
    }

    @Override
    public void updateVendor(FinaleVendorInfoDto finaleVendorInfoDto) {
        log.info("updateVendor finaleVendorInfoDto: {}", finaleVendorInfoDto);
        finaleExternalApiAdaptor.updateVendor(finaleVendorInfoDto);
    }

    private void syncStatus(String skuNumber, FinaleProductDto finaleProductDto, StatusId statusId) {
        if (finaleProductDto == null) {
            log.warn("Finale product is null, cannot sync status");
            return;
        }

        if (finaleProductDto.getStatusId().equals(statusId)) {
            log.info("Finale product status is already {}, skip sync", statusId);
            return;
        }
        log.info("Sync finale product status from {} to {}", finaleProductDto.getStatusId(), statusId);
        if (!StatusId.UNKNOWN.equals(statusId)) {
            finaleExternalApiAdaptor.updateItemStatus(skuNumber, statusId);
        }
    }

    private void syncVendorItem(ItemDto item, FinaleProductDto finaleProductDto, StatusId statusId) {
        if (StatusId.PRODUCT_INACTIVE.equals(statusId)) {
            log.info("Skip sync vendor item to finale since item is archived");
            return;
        }
        long inventory;
        if (item.getBackupVendorItem() != null
            && item.getBackupVendorItem().getAvailability() != null
            && Boolean.TRUE.equals(item.getBackupVendorItem().getAvailability())) {
            inventory = 9999L;
        } else {
            inventory = 0L;
        }

        Optional.ofNullable(item.getPrimaryVendorItem())
                .or(() -> Optional.ofNullable(item.getBackupVendorItem()))
                .ifPresentOrElse(vendorItemDto -> {
                    BigDecimal cost = vendorItemDto.isDirectVendorItem() ? vendorItemDto.getCost()
                            : vendorItemDto.getBackupCost();
                    if (null == cost) {
                        log.warn("Cost is null, skip sync vendor item to finale, item:{}", item);
                        return;
                    }

                    cost = cost.add(item.getCrvAmount());
                    finaleExternalApiAdaptor.updateVendorItem(item.getSkuNumber(), statusId, vendorItemDto.getVendorFinaleId(),
                            cost, inventory, vendorItemDto.getVendorSkuNumber(), finaleProductDto);
                }, () -> log.warn("Skip sync vendor item to finale, both primary and backup vendor items are null for item:{}", item));
    }
}
