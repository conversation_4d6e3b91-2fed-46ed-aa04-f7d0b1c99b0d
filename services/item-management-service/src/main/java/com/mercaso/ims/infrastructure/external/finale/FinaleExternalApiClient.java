package com.mercaso.ims.infrastructure.external.finale;

import static org.apache.hc.core5.http.HttpHeaders.AUTHORIZATION;
import static org.apache.hc.core5.http.HttpHeaders.CONTENT_TYPE;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mercaso.ims.domain.taskqueue.enums.TaskType;
import com.mercaso.ims.infrastructure.annotation.RateLimitedTask;
import com.mercaso.ims.infrastructure.client.HttpClient;
import com.mercaso.ims.infrastructure.exception.ErrorCodeEnums;
import com.mercaso.ims.infrastructure.exception.ImsBusinessException;
import com.mercaso.ims.infrastructure.external.finale.dto.*;
import com.mercaso.ims.infrastructure.external.finale.enums.StatusId;
import com.mercaso.ims.infrastructure.util.SerializationUtils;
import io.swagger.v3.oas.models.PathItem;
import io.swagger.v3.oas.models.PathItem.HttpMethod;
import java.io.IOException;
import java.util.Collections;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class FinaleExternalApiClient {

    private static final String CREATE_VENDOR_STATUS_ID = "PARTY_ENABLED";
    private static final String APPLICATION_JSON = "application/json";
    private final HttpClient client;
    @Value("${finale.create_vendor_url}")
    private String createVendorUrl;
    @Value("${finale.graphql_url}")
    private String graphqlUrl;
    @Value("${finale.query_vendors_url}")
    private String queryVendorUrl;
    @Value("${finale.product_url}")
    private String productUrl;
    @Value("${finale.get_purchase_order_url}")
    private String getPurchaseOrderUrl;
    @Value("${finale.update_vendor_url}")
    private String updateVendorUrl;


    @Value("${finale.token}")
    private String token;

    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
            taskType = TaskType.FINALE_GET_VENDORS,
            endpoint = "queryFinaleVendorById",
            synchronous = true
    )
    public FinaleVendorInfoDto queryFinaleVendorById(String id) {
        try {
            Request request = new Request.Builder()
                .url(queryVendorUrl + id)
                .method(HttpMethod.GET.name(), null)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            Response response = client.execute(request);
            log.info("[querySupplierById] end, response: {}", response);
            if (!response.isSuccessful()) {
                log.error("[querySupplierById] Failed to query supplier by id: {}", id);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            return SerializationUtils.deserialize(responseBody, FinaleVendorInfoDto.class);
        } catch (IOException e) {
            log.error("[querySupplierById] Failed to query supplier by id: {}", id, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED);
        }
    }

    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
            taskType = TaskType.FINALE_GET_VENDORS,
            endpoint = "queryFinaleVendors",
            synchronous = true
    )
    public QueryFinaleVendorsResultDto queryFinaleVendors() {
        try {
            Request request = new Request.Builder()
                .url(queryVendorUrl)
                .method(HttpMethod.GET.name(), null)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            Response response = client.execute(request);
            log.info("[queryFinaleVendors] end, response body: {}", response);
            if (!response.isSuccessful()) {
                log.error("[queryFinaleVendors] Failed to query Vendors from finale");
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            return SerializationUtils.deserialize(responseBody, QueryFinaleVendorsResultDto.class);
        } catch (IOException e) {
            log.error("[queryFinaleVendors] Failed to query Vendors from finale", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_VENDOR_FAILED);
        }
    }


    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
            taskType = TaskType.FINALE_CREATE_VENDOR,
            endpoint = "createVendor",
            httpMethod = "POST",
            synchronous = true
    )
    public FinaleVendorDto createVendor(String vendorName) {
        CreateVendorRequestDto createVendorRequestDto = buildCreateVendorRequestDto(vendorName);
        Response response;
        try {
            String jsonBody = SerializationUtils.serialize(createVendorRequestDto);
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(jsonBody, mediaType);
            Request request = new Request.Builder()
                .url(createVendorUrl)
                .method(PathItem.HttpMethod.POST.name(), body)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();

            log.info("[createVendor] Started, request body: {}", jsonBody);
            response = client.execute(request);
            log.info("[createVendor] end, response: {} ", response);
            if (!response.isSuccessful()) {
                log.error("[createVendor] Failed to create vendor for name: {}", vendorName);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_CREATE_VENDOR_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("[createVendor] end, responseBody: {} ", responseBody);
            return SerializationUtils.deserialize(responseBody, FinaleVendorDto.class);
        } catch (IOException e) {
            log.error("[createVendor]  Failed to create vendor for name: {}", vendorName, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_CREATE_VENDOR_FAILED);
        }
    }


    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
            taskType = TaskType.FINALE_UPDATE_VENDOR,
            endpoint = "updateVendor",
            httpMethod = "POST"
    )
    public void updateVendor(FinaleVendorInfoDto finaleVendorInfoDto) {
        String partyId = finaleVendorInfoDto.getPartyId();
        Response response;
        try {
            String jsonBody = SerializationUtils.serialize(finaleVendorInfoDto);
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(jsonBody, mediaType);
            Request request = new Request.Builder()
                    .url(updateVendorUrl + partyId)
                    .method(PathItem.HttpMethod.POST.name(), body)
                    .addHeader(AUTHORIZATION, this.token)
                    .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                    .build();

            log.info("[updateVendor] Started, request body: {}", jsonBody);
            response = client.execute(request);
            log.info("[updateVendor] end, response: {} ", response);
            if (!response.isSuccessful()) {
                log.error("[updateVendor] Failed to update vendor for partyId: {}", partyId);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_UPDATE_VENDOR_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("[updateVendor] end, responseBody: {} ", responseBody);
        } catch (IOException e) {
            log.error("[updateVendor]  Failed to update vendor for partyId: {}", partyId, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_UPDATE_VENDOR_FAILED);
        }
    }


    @RateLimitedTask(
            taskType = TaskType.FINALE_UPDATE_VENDOR_ITEM,
            endpoint = "updateVendorItem",
            httpMethod = "POST",
            needsResponse = false
    )
    public void updateVendorItem(String skuNumber, String status, UpdateSupplierItemRequestDto updateSupplierItemRequestDto) {
        Response response;
        try {
            String jsonBody = SerializationUtils.serialize(updateSupplierItemRequestDto);
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(jsonBody, mediaType);
            Request request = new Request.Builder()
                    .url(productUrl + skuNumber)
                    .method(PathItem.HttpMethod.POST.name(), body)
                    .addHeader(AUTHORIZATION, this.token)
                    .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                    .build();
            log.info("[updateVendorItem] Started, request body: {}", jsonBody);
            response = client.execute(request);
            log.info("[updateVendorItem] end, response: {} ", response);
            if (!response.isSuccessful()) {
                if (response.code() == 404) {
                    log.warn("[updateVendorItem] Item not found for skuNumber: {}, creating item first", skuNumber);
                    handleItemCreationForStatusInternal(skuNumber, status, "updateVendorItem");
                }

                log.error("[updateVendorItem] Failed to update vendor item for skuNumber: {}", skuNumber);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_UPDATE_VENDOR_ITEM_FAILED);

            } else {
                log.info("[updateVendorItem] end, responseBody: {} ", response.body().string());
            }
        } catch (IOException e) {
            log.error("[updateVendorItem] Failed to update vendor item for skuNumber: {}", skuNumber, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_UPDATE_VENDOR_ITEM_FAILED);
        }
    }


    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
            taskType = TaskType.FINALE_GET_PURCHASE_ORDERS,
            endpoint = "queryPurchaseOrders",
            synchronous = true,
            httpMethod = "POST"
    )
    public QueryFinalePurchaseOrderResultDto queryPurchaseOrders(int length) {
        try {
            String json =
                """
                    {
                        "operationName": "Rows",
                        "variables": {
                            "after": null,
                            "first": 100
                        },
                        "query": "query Rows($after: String, $first: Int) {\\n  orderViewConnection(type: [\\"PURCHASE_ORDER\\"],orderDate: {duration: \\"day\\", offset: -40, length: 41, timezone: \\"America/Los_Angeles\\"}, status: [\\"ORDER_COMPLETED\\"], after: $after, first: $first, sort: [{field: \\"orderDate\\", mode: \\"desc\\"}]) {\\n    summary {\\n      metrics {\\n        count\\n      }\\n    }\\n    edges {\\n      node(timezone: \\"America/Los_Angeles\\") {\\n        orderUrl\\n        fulfillment\\n        orderDateFormatted: orderDate\\n        orderDestination: destination {\\n          name\\n        }\\n        orderId        partySupplierGroupName: supplier {\\n          partyId\\n      name\\n       }\\n        receiveDateFormatted: receiveDate\\n        shipmentsFormatted: shipmentsSummary\\n        statusIdFormatted: status\\n        totalFormatted: total\\n      }\\n    }\\n    pageInfo {\\n      hasNextPage\\n      endCursor\\n    }\\n    strategy\\n  }\\n}\\n"
                    }
                    """;
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            String jsonWithLength = json.replace("41", String.valueOf(length));
            RequestBody body = RequestBody.create(jsonWithLength, mediaType);
            Request request = new Request.Builder()
                .url(graphqlUrl)
                .method(HttpMethod.POST.name(), body)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            Response response = client.execute(request);
            log.info("[queryPurchaseOrders] end, response body: {}", response);
            if (!response.isSuccessful()) {
                log.error("[queryPurchaseOrders] Failed to get Purchase Order from finale");
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            return SerializationUtils.deserialize(responseBody, QueryFinalePurchaseOrderResultDto.class);
        } catch (IOException e) {
            log.error("[queryPurchaseOrders] Failed to query Purchase Orders from finale", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
        }
    }


    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
            taskType = TaskType.FINALE_GET_PURCHASE_ORDER,
            endpoint = "getPurchaseOrder",
            synchronous = true
    )
    public FinalePurchaseOrderDto getPurchaseOrder(String orderId) {
        try {

            Request request = new Request.Builder()
                .url(getPurchaseOrderUrl + orderId)
                .method(HttpMethod.GET.name(), null)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            Response response = client.execute(request);
            log.info("[getPurchaseOrder] end, response body: {}", response);
            if (!response.isSuccessful()) {
                log.error("[getPurchaseOrder] Failed to get  Purchase Order from finale");
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            return SerializationUtils.deserialize(responseBody, FinalePurchaseOrderDto.class);
        } catch (IOException e) {
            log.error("[queryPurchaseOrders] Failed to query Purchase Order for :{}", orderId, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
        }
    }

    @RateLimitedTask(
            taskType = TaskType.FINALE_CREATE_ITEM,
            endpoint = "createFinaleItem",
            httpMethod = "POST",
            needsResponse = false
    )
    public void createFinaleItem(String skuNumber, String statusId) {
        executeCreateFinaleItem(skuNumber, statusId, "createFinaleItem");
    }

    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 2, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
            taskType = TaskType.FINALE_CHECK_ITEM_UNIQUE,
            endpoint = "skuIsUnique",
            httpMethod = "POST",
            synchronous = true,
            needsResponse = false
    )
    public boolean skuIsUnique(String skuNumber) {
        try {
            String json = String.format("""
                    {
                        "operationName": "IdIsUnique",
                        "query": "query IdIsUnique($id: [String]) { productViewConnection(productId: $id) { summary { metrics { count } } } }",
                        "variables": {
                            "id": ["%s"]
                        },
                        "sessionSecret": "itvBqZxEcddB9yLseXj8"
                    }
                """, skuNumber);

            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(json, mediaType);
            Request request = new Request.Builder()
                .url(graphqlUrl)
                .method(HttpMethod.POST.name(), body)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();

            Response response = client.execute(request);

            if (!response.isSuccessful()) {
                log.error("[skuIsUnique] Failed to query SKU from GraphQL, status: {}", response.code());
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_CHECK_SKU_FAILED);
            }

            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("[skuIsUnique] Response body: {}", responseBody);

            JsonNode rootNode = new ObjectMapper().readTree(responseBody);
            JsonNode countNode = rootNode.path("data")
                .path("productViewConnection")
                .path("summary")
                .path("metrics")
                .path("count");

            if (!countNode.isArray() || countNode.isEmpty()) {
                log.warn("[skuIsUnique] Invalid or missing count array in response.");
                return true;
            }

            int count = countNode.get(0).asInt(0);
            return count == 0;

        } catch (Exception e) {
            log.error("[skuIsUnique] Exception occurred when querying SKU uniqueness", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_CHECK_SKU_FAILED, e);
        }
    }

    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
            taskType = TaskType.FINALE_GET_PRODUCT,
            endpoint = "getFinaleProduct",
            synchronous = true
    )
    public FinaleProductDto getFinaleProduct(String skuNumber, String status) {
        try {
            Request request = new Request.Builder()
                .url(productUrl + skuNumber)
                .method(HttpMethod.GET.name(), null)
                .addHeader(AUTHORIZATION, this.token)
                .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                .build();
            Response response = client.execute(request);
            log.info("[getFinaleProduct] end, response body: {}", response);
            if (!response.isSuccessful()) {

                if (response.code() == 404) {
                    log.warn("[getFinaleProduct] Item not found for skuNumber: {}, creating item first", skuNumber);
                    handleItemCreationForStatusInternal(skuNumber, status, "getFinaleProduct");
                }

                log.error("[getFinaleProduct] Failed to get product from finale");
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_GET_PRODUCT_FAILED);
            }
            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("getFinaleProduct response body : {}", responseBody);
            return SerializationUtils.deserialize(responseBody, FinaleProductDto.class);
        } catch (IOException e) {
            log.error("[getFinaleProduct] Failed to get product from finale", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_GET_PRODUCT_FAILED);
        }
    }


    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
            taskType = TaskType.FINALE_UPDATE_ITEM,
            endpoint = "updateItemStatus",
            httpMethod = "POST",
            needsResponse = false
    )
    public void updateItemStatus(String skuNumber, String status) {
        Response response;
        try {
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create("", mediaType);
            Request request = new Request.Builder()
                    .url(productUrl + skuNumber + "/" + status)
                    .method(PathItem.HttpMethod.POST.name(), body)
                    .addHeader(AUTHORIZATION, this.token)
                    .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                    .build();
            log.info("[updateItemStatus] Started, change sku :{} status to: {}", skuNumber, status);
            response = client.execute(request);
            log.info("[updateItemStatus] end, response: {} ", response);
            if (!response.isSuccessful()) {
                if (response.code() == 404) {
                    log.warn("[updateItemStatus] Item not found for skuNumber: {}, creating item first", skuNumber);
                    handleItemCreationForStatusInternal(skuNumber, status, "updateItemStatus");
                }

                log.error("[updateItemStatus] Failed to update item status for skuNumber: {}", skuNumber);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_CHANGE_STATUS_FAILED);

            } else {
                log.info("[updateItemStatus] end, responseBody: {} ", response.body().string());
            }

        } catch (IOException e) {
            log.error("[updateItemStatus] Failed to update item status for skuNumber: {}", skuNumber, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_CHANGE_STATUS_FAILED);
        }
    }

    private CreateVendorRequestDto buildCreateVendorRequestDto(String vendorName) {
        return CreateVendorRequestDto.builder()
            .groupName(vendorName)
            .statusId(CREATE_VENDOR_STATUS_ID)
            .roleTypeIdList(Collections.singletonList("SUPPLIER"))
            .build();
    }


    @Retryable(retryFor = {ImsBusinessException.class}, maxAttempts = 3, backoff = @Backoff(delay = 5000))
    @RateLimitedTask(
            taskType = TaskType.FINALE_GET_PURCHASE_ORDERS,
            endpoint = "queryPurchaseOrders",
            synchronous = true
    )
    public FinaleOrderCollectionDto queryPurchaseOrdersByFilter(QueryFinalePurchaseOrderRequestDto queryFinalePurchaseOrderRequestDto) {
        try {
            // Build the URL with Base64 encoded filter parameter
            StringBuilder urlBuilder = new StringBuilder(getPurchaseOrderUrl);

            String requestFilter = queryFinalePurchaseOrderRequestDto.getBase64EncodedFilter();
            if (requestFilter != null && !requestFilter.isEmpty()) {
                urlBuilder.append("?filter=").append(requestFilter);
            }

            String finalUrl = urlBuilder.toString();
            log.info("[queryPurchaseOrders] Requesting URL: {}", finalUrl);

            Request request = new Request.Builder()
                    .url(finalUrl)
                    .method(HttpMethod.GET.name(), null)
                    .addHeader(AUTHORIZATION, this.token)
                    .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                    .build();

            Response response = client.execute(request);
            log.info("[queryPurchaseOrders] end, response: {}", response);

            if (!response.isSuccessful()) {
                log.error("[queryPurchaseOrders] Failed to get Purchase Order from finale, status code: {}", response.code());
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
            }

            assert response.body() != null;
            String responseBody = response.body().string();
            log.info("[queryPurchaseOrders] Response body: {}", responseBody);

            // Deserialize the response directly to our DTO
            return SerializationUtils.deserialize(responseBody, FinaleOrderCollectionDto.class);

        } catch (IOException e) {
            log.error("[queryPurchaseOrders] Failed to query Purchase Orders from finale", e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_QUERY_PURCHASE_ORDER_FAILED);
        }
    }

    /**
     * Internal method for handling item creation for status without rate limiting annotation
     * Used by processors to avoid infinite loop
     */
    private void handleItemCreationForStatusInternal(String skuNumber, String status, String methodName) {
        switch (status) {
            case "activate":
                executeCreateFinaleItem(skuNumber, StatusId.PRODUCT_ACTIVE.name(), methodName);
                log.info("[updateItemStatus] Item created successfully for skuNumber: {}", skuNumber);
                break;
            case "deactivate":
                executeCreateFinaleItem(skuNumber, StatusId.PRODUCT_INACTIVE.name(), methodName);
                log.info("[updateItemStatus] Item created successfully for skuNumber: {}", skuNumber);
                break;
            default:
                log.warn("[updateItemStatus] Unsupported status '{}' for creating item, skipping item creation for skuNumber: {}", status, skuNumber);
                break;
        }
    }

    private void executeCreateFinaleItem(String skuNumber, String statusId, String methodName) {
        try {
            CreateFinaleItemRequestDto createFinaleItemRequestDto = new CreateFinaleItemRequestDto(skuNumber, statusId);
            String jsonBody = SerializationUtils.serialize(createFinaleItemRequestDto);
            MediaType mediaType = MediaType.parse(APPLICATION_JSON);
            RequestBody body = RequestBody.create(jsonBody, mediaType);
            Request request = new Request.Builder()
                    .url(productUrl)
                    .method(PathItem.HttpMethod.POST.name(), body)
                    .addHeader(AUTHORIZATION, this.token)
                    .addHeader(CONTENT_TYPE, APPLICATION_JSON)
                    .build();

            log.info("[{}] Started, request body: {}", methodName, jsonBody);
            Response response = client.execute(request);
            log.info("[{}] end, response: {} ", methodName, response);
            if (!response.isSuccessful()) {
                log.error("[{}] Failed to creat finale item for skuNumber: {}", methodName, skuNumber);
                throw new ImsBusinessException(ErrorCodeEnums.FINALE_CREATE_ITEM_FAILED);
            }
            assert response.body() != null;
            log.info("[{}] end, responseBody: {} ", methodName, response.body().string());
        } catch (IOException e) {
            log.error("[{}] Failed to creat finale item for skuNumber:  :{}", methodName, skuNumber, e);
            throw new ImsBusinessException(ErrorCodeEnums.FINALE_CREATE_ITEM_FAILED);
        }
    }
}
