package com.mercaso.ims.utils.itemvendorrebate;

import com.mercaso.ims.domain.itemvendorrebate.ItemVendorRebate;
import com.mercaso.ims.domain.itemvendorrebate.enums.ItemVendorRebateStatus;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Utility class for building ItemVendorRebate test data
 */
public class ItemVendorRebateUtil {

    /**
     * Build a basic ItemVendorRebate with default values
     */
    public static ItemVendorRebate buildItemVendorRebate(UUID vendorItemId, UUID vendorId, UUID itemId) {
        return ItemVendorRebate.builder()
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(LocalDate.now())
                .endDate(LocalDate.now().plusMonths(6))
                .rebatePerUnit(new BigDecimal("5.00"))
                .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
                .build();
    }

    /**
     * Build an ItemVendorRebate with custom dates and rebate amount
     */
    public static ItemVendorRebate buildItemVendorRebate(UUID vendorItemId, UUID vendorId, UUID itemId,
                                                         LocalDate startDate, LocalDate endDate, BigDecimal rebatePerUnit) {
        return ItemVendorRebate.builder()
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(startDate)
                .endDate(endDate)
                .rebatePerUnit(rebatePerUnit)
                .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
                .build();
    }

    /**
     * Build a continuous ItemVendorRebate (no end date)
     */
    public static ItemVendorRebate buildContinuousItemVendorRebate(UUID vendorItemId, UUID vendorId, UUID itemId,
                                                                   LocalDate startDate, BigDecimal rebatePerUnit) {
        return ItemVendorRebate.builder()
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(startDate)
                .endDate(null) // Continuous rebate
                .rebatePerUnit(rebatePerUnit)
                .itemVendorRebateStatus(ItemVendorRebateStatus.ACTIVE)
                .build();
    }

    /**
     * Build an ItemVendorRebate with specific status
     */
    public static ItemVendorRebate buildItemVendorRebateWithStatus(UUID vendorItemId, UUID vendorId, UUID itemId,
                                                                   ItemVendorRebateStatus status) {
        return ItemVendorRebate.builder()
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(LocalDate.now())
                .endDate(LocalDate.now().plusMonths(6))
                .rebatePerUnit(new BigDecimal("5.00"))
                .itemVendorRebateStatus(status)
                .build();
    }

    /**
     * Build an ItemVendorRebate with all custom parameters
     */
    public static ItemVendorRebate buildItemVendorRebate(UUID vendorItemId, UUID vendorId, UUID itemId,
                                                         LocalDate startDate, LocalDate endDate, BigDecimal rebatePerUnit,
                                                         ItemVendorRebateStatus status) {
        return ItemVendorRebate.builder()
                .vendorItemId(vendorItemId)
                .vendorId(vendorId)
                .itemId(itemId)
                .startDate(startDate)
                .endDate(endDate)
                .rebatePerUnit(rebatePerUnit)
                .itemVendorRebateStatus(status)
                .build();
    }
}
