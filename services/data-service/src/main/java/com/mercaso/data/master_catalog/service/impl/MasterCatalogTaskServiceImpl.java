package com.mercaso.data.master_catalog.service.impl;

import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.mapper.MasterCatalogTaskMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogTaskService;
import java.util.ArrayList;
import java.util.List;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import java.util.UUID;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class MasterCatalogTaskServiceImpl implements MasterCatalogTaskService {

  private final MasterCatalogTaskMapper masterCatalogTaskMapper;

  private final MasterCatalogTaskRepository masterCatalogTaskRepository;

  @Override
  public List<MasterCatalogTaskDto> createTasks(UUID jobId, MasterCatalogTaskType type, Integer taskCount) {
    if (taskCount < 1) {
      return List.of();
    }
    List<MasterCatalogTask> tasks = new ArrayList<>();
    for (int i = 0; i < taskCount; i++) {
      MasterCatalogTask task = MasterCatalogTask.builder()
          .jobId(jobId).
          status(MasterCatalogTaskStatus.PENDING)
          .type(type)
          .build();
      tasks.add(task);
    }

    return masterCatalogTaskRepository.saveAll(tasks).stream().map(masterCatalogTaskMapper::toDto)
        .collect(Collectors.toList());
  }

  @Override
  public void assign(UUID id, String assignTo) {
    MasterCatalogTask task = masterCatalogTaskRepository.findById(id).orElseThrow(
        () -> new IllegalArgumentException("task not fount with id:" + id));
    validateTask(task);

    task.setAssignedBy(SecurityContextUtil.getLoginUserId());
    task.setAssignedTo(assignTo);
    task.setStatus(MasterCatalogTaskStatus.ASSIGNED);
    masterCatalogTaskRepository.save(task);
    log.info("Task with id {} has been assigned to {}", id, assignTo);
  }

  private void validateTask(MasterCatalogTask task) {
    if (MasterCatalogTaskStatus.PENDING != task.getStatus()
        && MasterCatalogTaskStatus.ASSIGNED != task.getStatus()) {
      log.warn("The task with id {} is not in available status, cannot update.", task.getId());
      throw new IllegalArgumentException("the task is not a available task");
    }
  }
}
