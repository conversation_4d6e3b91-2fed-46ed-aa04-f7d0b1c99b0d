package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.master_catalog.service.MasterCatalogTaskService;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/master-catalog/v1/tasks")
@RequiredArgsConstructor
@Validated
public class MasterCatalogTaskResource {

  private final MasterCatalogTaskService masterCatalogTaskService;

  @PreAuthorize("hasAnyAuthority('master-catalog:write:tasks')")
  @PutMapping("/assign/{id}")
  public void assign(
      @PathVariable("id") String id,
      @RequestParam("assign_to") String assignTo) {
    masterCatalogTaskService.assign(UUID.fromString(id), assignTo);
  }
}
