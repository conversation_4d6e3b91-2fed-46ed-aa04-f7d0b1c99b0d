package com.mercaso.data.master_catalog.event.applicationevent.listener;

import com.mercaso.data.master_catalog.dto.MasterCatalogBatchJobDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogPotentiallyDuplicateRawData;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogRawDataDuplication;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.PotentiallyDuplicateRawDataStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.event.applicationevent.api.ApplicationEventListener;
import com.mercaso.data.master_catalog.event.applicationevent.publisher.ApplicationEventPublisherProvider;
import com.mercaso.data.master_catalog.event.model.domain.RemoveDuplicationSubmitEvent;
import com.mercaso.data.master_catalog.event.payload.domain.RemoveDuplicationSubmitPayload;
import com.mercaso.data.master_catalog.mapper.MasterCatalogBatchJobMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogPotentiallyDuplicateRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataDuplicationRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogRawDataRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.MasterCatalogRawDataDuplicationService;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RemoveDuplicationSubmitEventListener extends AbstractSubmitDuplicationEventListener
    implements ApplicationEventListener<RemoveDuplicationSubmitEvent, RemoveDuplicationSubmitPayload> {

    public RemoveDuplicationSubmitEventListener(
        MasterCatalogPotentiallyDuplicateRawDataRepository masterCatalogPotentiallyDuplicateRawDataRepository,
        MasterCatalogRawDataDuplicationRepository masterCatalogRawDataDuplicationRepository,
        MasterCatalogRawDataDuplicationService masterCatalogRawDataDuplicationService,
        MasterCatalogBatchJobRepository masterCatalogBatchJobRepository,
        MasterCatalogBatchJobMapper masterCatalogBatchJobMapper,
        ApplicationEventPublisherProvider applicationEventPublisherProvider,
        MasterCatalogRawDataRepository masterCatalogRawDataRepository,
        MasterCatalogTaskRepository masterCatalogTaskRepository) {
        super(masterCatalogPotentiallyDuplicateRawDataRepository, masterCatalogRawDataDuplicationRepository,
            masterCatalogRawDataDuplicationService, masterCatalogBatchJobRepository,
            masterCatalogBatchJobMapper, applicationEventPublisherProvider, masterCatalogRawDataRepository,
            masterCatalogTaskRepository);
    }

    @Override
    public void handleEvent(RemoveDuplicationSubmitEvent event) {
        PotentiallyDuplicationContext context = createContext(event.getPayload());
        processRemoveDuplicationSubmit(context);
    }

    private void processRemoveDuplicationSubmit(PotentiallyDuplicationContext context) {
        List<MasterCatalogPotentiallyDuplicateRawData> potentiallyDuplicateRawDataList = context.potentiallyDuplicateData();
        if (anyMatchUnreviewedStatus(potentiallyDuplicateRawDataList)) {
            log.info("Job {} contains unreviewed data, skipping processing", context.jobId());
            return;
        }

        List<MasterCatalogPotentiallyDuplicateRawData> markedDuplicates =
            getMarkedDuplicates(potentiallyDuplicateRawDataList);

        if (!markedDuplicates.isEmpty()) {
            processDuplicationRecords(context.jobId(), markedDuplicates);
        }

        checkIfUpdateTaskStatus(potentiallyDuplicateRawDataList);

        boolean allSubmitted = masterCatalogPotentiallyDuplicateRawDataRepository.findAllByJobId(context.jobId())
            .stream().allMatch(data -> PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED == data.getStatus());

        if (allSubmitted) {
            updateJobStatus(context);

            applicationEventPublisherProvider.publishEventRemoveDuplicationCompleted(context.job(),
                potentiallyDuplicateRawDataList);
        }
    }

    private void processDuplicationRecords(UUID jobId,
        List<MasterCatalogPotentiallyDuplicateRawData> markedDuplicates) {
        List<MasterCatalogRawDataDuplication> duplicationRecords = createDuplicationRecords(markedDuplicates);

        if (!duplicationRecords.isEmpty()) {
            masterCatalogRawDataDuplicationRepository.saveAll(duplicationRecords);
            log.info("Saved {} duplication records for job {}", duplicationRecords.size(), jobId);
        }
    }

    private PotentiallyDuplicationContext createContext(RemoveDuplicationSubmitPayload payload) {
        MasterCatalogBatchJobDto jobDto = payload.getData();
        MasterCatalogBatchJob job = masterCatalogBatchJobMapper.toEntity(jobDto);
        List<MasterCatalogPotentiallyDuplicateRawData> duplicateData =
            masterCatalogPotentiallyDuplicateRawDataRepository.findAllByJobId(jobDto.getId());

        return new PotentiallyDuplicationContext(jobDto.getId(), job, duplicateData);
    }

    @Override
    protected boolean anyMatchUnreviewedStatus(List<MasterCatalogPotentiallyDuplicateRawData> duplicateData) {
        return duplicateData.stream()
            .anyMatch(data -> data.getStatus() != PotentiallyDuplicateRawDataStatus.FIRST_ROUND_REVIEWED);
    }

    @Override
    protected void updateJobStatus(PotentiallyDuplicationContext context) {
        MasterCatalogBatchJob job = context.job();
        job.setStatus(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_COMPLETED);
        masterCatalogBatchJobRepository.save(job);
        log.info("Job {} remove duplication completed with status {}", job.getId(), job.getStatus());
    }
}
