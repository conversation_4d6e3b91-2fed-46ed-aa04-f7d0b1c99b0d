package com.mercaso.data.master_catalog.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.mercaso.data.master_catalog.dto.MasterCatalogTaskDto;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.mapper.MasterCatalogTaskMapper;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.service.impl.MasterCatalogTaskServiceImpl;
import com.mercaso.security.auth0.utils.SecurityContextUtil;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MasterCatalogTaskServiceImplTest {

  @Mock
  private MasterCatalogTaskMapper masterCatalogTaskMapper;

  @Mock
  private MasterCatalogTaskRepository masterCatalogTaskRepository;

  @InjectMocks
  private MasterCatalogTaskServiceImpl masterCatalogTaskService;

  private UUID taskId;
  private UUID jobId;
  private MasterCatalogTask task;
  private MasterCatalogTaskDto taskDto;

  @BeforeEach
  void setUp() {
    taskId = UUID.randomUUID();
    jobId = UUID.randomUUID();

    task = MasterCatalogTask.builder()
        .id(taskId)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();

    taskDto = MasterCatalogTaskDto.builder()
        .id(taskId)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .build();
  }

  @Test
  void createTasks_ShouldCreateTasksSuccessfully() {

    Integer taskCount = 3;
    List<MasterCatalogTask> savedTasks = List.of(task, task, task);

    when(masterCatalogTaskRepository.saveAll(anyList())).thenReturn(savedTasks);
    when(masterCatalogTaskMapper.toDto(any(MasterCatalogTask.class))).thenReturn(taskDto);

    List<MasterCatalogTaskDto> result = masterCatalogTaskService.createTasks(jobId,
        MasterCatalogTaskType.DUPLICATION_IN_BATCH, taskCount);

    // Then
    assertEquals(3, result.size());
    verify(masterCatalogTaskRepository).saveAll(argThat((List<MasterCatalogTask> tasks) ->
        tasks.size() == 3 &&
        tasks.stream().allMatch(t ->
            t.getJobId().equals(jobId) &&
            t.getStatus() == MasterCatalogTaskStatus.PENDING &&
            t.getType() == MasterCatalogTaskType.DUPLICATION_IN_BATCH
        )
    ));
    verify(masterCatalogTaskMapper, times(3)).toDto(any(MasterCatalogTask.class));
  }

  @Test
  void assign_WhenTaskExistsAndPending_ShouldAssignSuccessfully() {
    // Given
    String assignedTo = UUID.randomUUID().toString();
    String currentUserId = UUID.randomUUID().toString();

    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.of(task));
    when(masterCatalogTaskRepository.save(any(MasterCatalogTask.class))).thenReturn(task);

    try (MockedStatic<SecurityContextUtil> mockedSecurityContext = mockStatic(SecurityContextUtil.class)) {
      mockedSecurityContext.when(SecurityContextUtil::getLoginUserId).thenReturn(currentUserId);

      masterCatalogTaskService.assign(taskId, assignedTo);

      verify(masterCatalogTaskRepository).save(argThat(savedTask ->
          assignedTo.equals(savedTask.getAssignedTo()) &&
          currentUserId.equals(savedTask.getAssignedBy()) &&
          savedTask.getStatus() == MasterCatalogTaskStatus.ASSIGNED
      ));
    }
  }

  @Test
  void assign_WhenTaskNotFound_ShouldThrowException() {

    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.empty());

    assertThrows(IllegalArgumentException.class,
        () -> masterCatalogTaskService.assign(taskId, UUID.randomUUID().toString())
        , "task not fount with id");
  }

  @Test
  void assign_WhenTaskNotPending_ShouldThrowException() {

    task.setStatus(MasterCatalogTaskStatus.IN_PROGRESS);
    when(masterCatalogTaskRepository.findById(taskId)).thenReturn(Optional.of(task));

    assertThrows(IllegalArgumentException.class,
        () -> masterCatalogTaskService.assign(taskId, UUID.randomUUID().toString())
        , "the task is not a available task");
  }
}
