package com.mercaso.data.master_catalog.utils.resource_utils;


import com.mercaso.data.utils.IntegrationTestRestUtil;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MasterCatalogTaskResourceApiUtils extends IntegrationTestRestUtil {

  public static final String V1_TASKS = "/master-catalog/v1/tasks/assign/";

  public MasterCatalogTaskResourceApiUtils(Environment environment) {
    super(environment);
  }

  public void assign(String id, String assignTo) {
    String url = V1_TASKS + id;

    Map<String, String> params = new HashMap<>();
    params.put("assign_to", assignTo);

    try {
      performRequest(url, params, null, HttpMethod.PUT);
    } catch (Exception e) {
      log.error("Error updating potentially duplicate raw data: {}", e.getMessage(), e);
    }
  }
}
