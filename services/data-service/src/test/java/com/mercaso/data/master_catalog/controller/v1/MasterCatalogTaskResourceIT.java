package com.mercaso.data.master_catalog.controller.v1;

import com.mercaso.data.AbstractIT;
import com.mercaso.data.master_catalog.entity.MasterCatalogBatchJob;
import com.mercaso.data.master_catalog.entity.MasterCatalogTask;
import com.mercaso.data.master_catalog.enums.MasterCatalogBatchJobStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskStatus;
import com.mercaso.data.master_catalog.enums.MasterCatalogTaskType;
import com.mercaso.data.master_catalog.repository.MasterCatalogBatchJobRepository;
import com.mercaso.data.master_catalog.repository.MasterCatalogTaskRepository;
import com.mercaso.data.master_catalog.utils.resource_utils.MasterCatalogTaskResourceApiUtils;
import java.util.UUID;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class MasterCatalogTaskResourceIT extends AbstractIT {

  @Autowired
  private MasterCatalogTaskResourceApiUtils masterCatalogTaskResourceApiUtils;
  @Autowired
  private MasterCatalogTaskRepository masterCatalogTaskRepository;
  @Autowired
  private MasterCatalogBatchJobRepository masterCatalogBatchJobRepository;

  @Test
  void assign() {
    UUID jobId = UUID.randomUUID();
    String taskId = UUID.randomUUID().toString();
    MasterCatalogTask task = MasterCatalogTask.builder()
        .id(UUID.fromString(taskId))
        .type(MasterCatalogTaskType.DUPLICATION_IN_BATCH)
        .jobId(jobId)
        .status(MasterCatalogTaskStatus.PENDING)
        .build();

    MasterCatalogBatchJob job = MasterCatalogBatchJob.builder().id(jobId)
        .status(MasterCatalogBatchJobStatus.REMOVE_DUPLICATION_IN_PROGRESS).build();

    masterCatalogTaskRepository.save(task);
    masterCatalogBatchJobRepository.save(job);

    masterCatalogTaskResourceApiUtils.assign(taskId, "zhangsan");

    MasterCatalogTask masterCatalogTask = masterCatalogTaskRepository.findById(UUID.fromString(taskId)).orElseThrow();

    assert masterCatalogTask.getId().equals(UUID.fromString(taskId));
    assert masterCatalogTask.getStatus().equals(MasterCatalogTaskStatus.ASSIGNED);
    assert masterCatalogTask.getAssignedTo().equals("zhangsan");
  }
}
