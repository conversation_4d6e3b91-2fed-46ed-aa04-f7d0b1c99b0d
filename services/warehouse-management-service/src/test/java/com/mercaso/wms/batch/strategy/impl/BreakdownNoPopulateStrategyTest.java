package com.mercaso.wms.batch.strategy.impl;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.mercaso.wms.batch.constants.BatchConstants;
import com.mercaso.wms.batch.dto.ExcelBatchDto;
import com.mercaso.wms.batch.dto.PopulateCondition;
import com.mercaso.wms.batch.enums.SourceEnum;
import com.mercaso.wms.domain.location.Location;
import com.mercaso.wms.domain.location.enums.LocationType;
import com.mercaso.wms.domain.warehouse.Warehouse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.commons.compress.utils.Lists;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.util.CollectionUtils;

class BreakdownNoPopulateStrategyTest {

    private BreakdownNoPopulateStrategy strategy;

    @BeforeEach
    void setUp() {
        strategy = new BreakdownNoPopulateStrategy();
    }

    @Test
    void when_batchDtoList_is_empty_then_return_empty_list() {
        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(Lists.newArrayList())
            .locations(Lists.newArrayList())
                .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    void when_match_breakdown_then_match_shipping_big_breakdown() {
        List<ExcelBatchDto> excelBatchDtos = getBatchDtos();
        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtos)
            .locations(List.of(Location.builder()
                .name("A-01")
                .type(LocationType.SHIPPING_BIG)
                .warehouse(Warehouse.builder()
                    .name("MDC")
                    .build())
                .build()))
                .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertEquals("A-01", result.getFirst().getPos());
    }

    @Test
    void when_delivery_date_change_dto_is_empty() {
        List<ExcelBatchDto> excelBatchDtos = getBatchDtos(5);
        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtos)
            .locations(Lists.newArrayList())
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertEquals(excelBatchDtos, result);
    }

    @Test
    void when_new_delivery_date_is_not_within_interval() {
        List<ExcelBatchDto> excelBatchDtos = getBatchDtos(10);
        ExcelBatchDto last = excelBatchDtos.getLast();
        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtos)
            .deliveryDate("2024-08-24")
            .locations(Lists.newArrayList())
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertEquals(excelBatchDtos, result);
        assertNull(last.getColourMarking());
    }

    @Test
    void when_new_delivery_date_is_later_than_interval() {
        List<ExcelBatchDto> excelBatchDtos = getBatchDtos(10);
        ExcelBatchDto last = excelBatchDtos.getLast();
        PopulateCondition populateCondition = PopulateCondition.builder()
            .excelBatchDtoList(excelBatchDtos)
            .deliveryDate("2024-09-01")
            .locations(Lists.newArrayList())
            .build();

        List<ExcelBatchDto> result = strategy.populateBatchTemplate(populateCondition);

        assertEquals(excelBatchDtos, result);
        assertNull(last.getColourMarking());
    }

    @Test
    void getExcelBatchGroupedByQuantity_ShouldGroupByQuantityAndSortByDistrict() {
        // Given
        List<ExcelBatchDto> batchDtos = Arrays.asList(
            createExcelBatchDtoWithDepartment("order1", "District B", 6, SourceEnum.MDC.name(), BatchConstants.BEVERAGE),
            createExcelBatchDto("order1", "District B", 4, SourceEnum.MFC.name()),
            createExcelBatchDtoWithDepartment("order2", "District A", 3, SourceEnum.MDC.name(), BatchConstants.BEVERAGE),
            createExcelBatchDto("order2", "District A", 7, SourceEnum.MFC.name()),
            createExcelBatchDtoWithDepartment("order3", "District C", 5, SourceEnum.MDC.name(), BatchConstants.BEVERAGE),
            createExcelBatchDto("order4", "District A", 5, SourceEnum.MFC.name())
        );

        // When
        Map<String, List<ExcelBatchDto>> result = strategy.getExcelBatchGroupedByQuantity(batchDtos);

        // Then
        assertNotNull(result);
        assertEquals(1, result.get("order1").stream().filter(ExcelBatchDto::isBigOrder).count());
        assertEquals(0, result.get("order2").stream().filter(ExcelBatchDto::isBigOrder).count());
        assertEquals(0, result.get("order3").stream().filter(ExcelBatchDto::isBigOrder).count());
        assertEquals(0, result.get("order4").stream().filter(ExcelBatchDto::isBigOrder).count());

        assertEquals(1, result.get("order1").stream().filter(excelBatchDto -> !excelBatchDto.isBigOrder()).count());
        assertEquals(2, result.get("order2").stream().filter(excelBatchDto -> !excelBatchDto.isBigOrder()).count());
        assertEquals(1, result.get("order3").stream().filter(excelBatchDto -> !excelBatchDto.isBigOrder()).count());
        assertEquals(1, result.get("order4").stream().filter(excelBatchDto -> !excelBatchDto.isBigOrder()).count());

        // Verify district sorting
        String[] expectedOrderForSmallOrders = {"order2", "order4", "order1", "order3"}; // Both from District A
        assertArrayEquals(expectedOrderForSmallOrders, result.keySet().toArray());
    }

    @Test
    void getExcelBatchGroupedByQuantity_ShouldHandleNullDistricts() {
        // Given
        List<ExcelBatchDto> batchDtos = Arrays.asList(
            createExcelBatchDtoWithDepartment("order1", "District B", 16, SourceEnum.MDC.name(), BatchConstants.BEVERAGE),
            createExcelBatchDtoWithDepartment("order2", "District A", 14, SourceEnum.MDC.name(), BatchConstants.BEVERAGE),
            createExcelBatchDtoWithDepartment("order3", null, 5, SourceEnum.MDC.name(), BatchConstants.BEVERAGE)
        );

        // When
        Map<String, List<ExcelBatchDto>> result = strategy.getExcelBatchGroupedByQuantity(batchDtos);



        // Then
        assertNotNull(result);
        //result to List
        List<ExcelBatchDto> resultList = result.values().stream()
            .flatMap(List::stream)
            .toList();
        assertEquals("District A", resultList.getFirst().getDistrictName());
        assertEquals("order2", resultList.getFirst().getOrderNumber());// District A should be first

        assertEquals("District B", resultList.get(1).getDistrictName());
        assertEquals("order1", resultList.get(1).getOrderNumber());

        assertEquals("order3", resultList.get(2).getOrderNumber()); // null district should be last
    }

    @Test
    void getExcelBatchGroupedByQuantity_ShouldHandleEmptyList() {
        // Given
        List<ExcelBatchDto> emptyList = List.of();

        // When
        Map<String, List<ExcelBatchDto>> result = strategy.getExcelBatchGroupedByQuantity(emptyList);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void getExcelBatchGroupedByQuantity_ShouldExcludeCandyAndSnacksFromBigOrderCalculation() {
        // Given
        List<ExcelBatchDto> batchDtos = Arrays.asList(
            createExcelBatchDtoWithDepartment("order1", "District A", 3, SourceEnum.MDC.name(), BatchConstants.BEVERAGE),
            createExcelBatchDtoWithDepartment("order1", "District A", 4, SourceEnum.MDC.name(), BatchConstants.CANDY_AND_SNACKS),
            createExcelBatchDtoWithDepartment("order1",
                "District A",
                2,
                SourceEnum.MDC.name(),
                BatchConstants.CLEANING_AND_LAUNDRY)
        );

        // When
        Map<String, List<ExcelBatchDto>> result = strategy.getExcelBatchGroupedByQuantity(batchDtos);

        // Then
        assertNotNull(result);
        List<ExcelBatchDto> order1Items = result.get("order1");
        assertEquals(3, order1Items.size());

        // Only beverage and cleaning items should count (3+2=5), candy & snacks (4) should be excluded
        // Since sum = 5 (not > 5), bigOrder should be false for all items
        long bigOrderCount = order1Items.stream().filter(ExcelBatchDto::isBigOrder).count();
        assertEquals(0, bigOrderCount);
    }

    @Test
    void getExcelBatchGroupedByQuantity_ShouldMarkAsBigOrderWhenExcludingCandyAndSnacksStillExceedsThreshold() {
        // Given
        List<ExcelBatchDto> batchDtos = Arrays.asList(
            createExcelBatchDtoWithDepartment("order1", "District A", 4, SourceEnum.MDC.name(), BatchConstants.BEVERAGE),
            createExcelBatchDtoWithDepartment("order1", "District A", 10, SourceEnum.MDC.name(), BatchConstants.CANDY_AND_SNACKS),
            createExcelBatchDtoWithDepartment("order1",
                "District A",
                3,
                SourceEnum.MDC.name(),
                BatchConstants.CLEANING_AND_LAUNDRY)
        );

        // When
        Map<String, List<ExcelBatchDto>> result = strategy.getExcelBatchGroupedByQuantity(batchDtos);

        // Then
        assertNotNull(result);
        List<ExcelBatchDto> order1Items = result.get("order1");
        assertEquals(3, order1Items.size());

        // Only beverage and cleaning items should count (4+3=7 > 5), candy & snacks (10) should be excluded
        // MDC items (beverage and cleaning) should be marked as bigOrder
        long mdcBigOrderCount = order1Items.stream()
            .filter(item -> SourceEnum.MDC.name().equals(item.getSource()))
            .filter(item -> !BatchConstants.CANDY_AND_SNACKS.equals(item.getDepartment()))
            .filter(ExcelBatchDto::isBigOrder)
            .count();
        assertEquals(2, mdcBigOrderCount);

        // Candy & snacks item should not be marked as bigOrder
        boolean candyItemIsBigOrder = order1Items.stream()
            .filter(item -> BatchConstants.CANDY_AND_SNACKS.equals(item.getDepartment()))
            .anyMatch(ExcelBatchDto::isBigOrder);
        assertFalse(candyItemIsBigOrder);
    }

    @Test
    void getExcelBatchGroupedByQuantity_ShouldHandleMixedDepartmentsCorrectly() {
        // Given
        List<ExcelBatchDto> batchDtos = Arrays.asList(
            // Order 1: Mixed departments, should be big order (6+2=8 > 5, excluding candy)
            createExcelBatchDtoWithDepartment("order1", "District A", 6, SourceEnum.MDC.name(), BatchConstants.BEVERAGE),
            createExcelBatchDtoWithDepartment("order1", "District A", 5, SourceEnum.MDC.name(), BatchConstants.CANDY_AND_SNACKS),
            createExcelBatchDtoWithDepartment("order1",
                "District A",
                2,
                SourceEnum.MDC.name(),
                BatchConstants.CLEANING_AND_LAUNDRY),

            // Order 2: Only candy & snacks, should not be big order (0 after exclusion)
            createExcelBatchDtoWithDepartment("order2", "District B", 10, SourceEnum.MDC.name(), BatchConstants.CANDY_AND_SNACKS),

            // Order 3: Non-MDC items, should not be affected
            createExcelBatchDtoWithDepartment("order3", "District C", 8, SourceEnum.MFC.name(), BatchConstants.CANDY_AND_SNACKS)
        );

        // When
        Map<String, List<ExcelBatchDto>> result = strategy.getExcelBatchGroupedByQuantity(batchDtos);

        // Then
        assertNotNull(result);

        // Order 1: Should have big order items (excluding candy)
        List<ExcelBatchDto> order1Items = result.get("order1");
        long order1BigOrderCount = order1Items.stream()
            .filter(item -> !BatchConstants.CANDY_AND_SNACKS.equals(item.getDepartment()))
            .filter(ExcelBatchDto::isBigOrder)
            .count();
        assertEquals(2, order1BigOrderCount);

        // Order 2: Should not have any big order items
        List<ExcelBatchDto> order2Items = result.get("order2");
        long order2BigOrderCount = order2Items.stream().filter(ExcelBatchDto::isBigOrder).count();
        assertEquals(0, order2BigOrderCount);

        // Order 3: Non-MDC items should not be affected by the filter
        List<ExcelBatchDto> order3Items = result.get("order3");
        long order3BigOrderCount = order3Items.stream().filter(ExcelBatchDto::isBigOrder).count();
        assertEquals(0, order3BigOrderCount); // MFC items don't get bigOrder flag in this logic
    }

    @Test
    void getExcelBatchGroupedByQuantity_ShouldHandleEmptyDepartmentCorrectly() {
        // Given
        List<ExcelBatchDto> batchDtos = Arrays.asList(
            createExcelBatchDtoWithDepartment("order1", "District A", 3, SourceEnum.MDC.name(), null),
            createExcelBatchDtoWithDepartment("order1", "District A", 4, SourceEnum.MDC.name(), ""),
            createExcelBatchDtoWithDepartment("order1", "District A", 2, SourceEnum.MDC.name(), BatchConstants.BEVERAGE)
        );

        // When
        Map<String, List<ExcelBatchDto>> result = strategy.getExcelBatchGroupedByQuantity(batchDtos);

        // Then
        assertNotNull(result);
        List<ExcelBatchDto> order1Items = result.get("order1");
        assertEquals(3, order1Items.size());

        // Items with null or empty department should be filtered out, only beverage item (2) should count
        // Since 2 <= 5, no items should be marked as bigOrder
        long bigOrderCount = order1Items.stream().filter(ExcelBatchDto::isBigOrder).count();
        assertEquals(0, bigOrderCount);
    }

    @Test
    void getExcelBatchGroupedByQuantity_ShouldOnlyApplyFilterToMDCItems() {
        // Given
        List<ExcelBatchDto> batchDtos = Arrays.asList(
            // MDC items with candy & snacks - should be filtered
            createExcelBatchDtoWithDepartment("order1", "District A", 3, SourceEnum.MDC.name(), BatchConstants.BEVERAGE),
            createExcelBatchDtoWithDepartment("order1", "District A", 5, SourceEnum.MDC.name(), BatchConstants.CANDY_AND_SNACKS),

            // MFC items with candy & snacks - should NOT be filtered (different source)
            createExcelBatchDtoWithDepartment("order2", "District B", 3, SourceEnum.MFC.name(), BatchConstants.BEVERAGE),
            createExcelBatchDtoWithDepartment("order2", "District B", 5, SourceEnum.MFC.name(), BatchConstants.CANDY_AND_SNACKS)
        );

        // When
        Map<String, List<ExcelBatchDto>> result = strategy.getExcelBatchGroupedByQuantity(batchDtos);

        // Then
        assertNotNull(result);

        // Order 1 (MDC): Only beverage item (3) should count, candy excluded
        List<ExcelBatchDto> order1Items = result.get("order1");
        long order1BigOrderCount = order1Items.stream().filter(ExcelBatchDto::isBigOrder).count();
        assertEquals(0, order1BigOrderCount); // 3 <= 5

        // Order 2 (MFC): Should not be affected by MDC-specific logic
        List<ExcelBatchDto> order2Items = result.get("order2");
        long order2BigOrderCount = order2Items.stream().filter(ExcelBatchDto::isBigOrder).count();
        assertEquals(0, order2BigOrderCount); // MFC items don't get processed for bigOrder in this method
    }

    @NotNull
    private static List<ExcelBatchDto> getBatchDtos() {
        List<ExcelBatchDto> excelBatchDtos = Lists.newArrayList();
        for (int i = 0; i < 15; i++) {
            excelBatchDtos.add(ExcelBatchDto.builder()
                .orderNumber("M-12345")
                .itemNumber("JC12345")
                .line(i + 1)
                .quantity(1)
                .pos(i + 1 + "")
                .build());
        }
        return excelBatchDtos;
    }

    @NotNull
    private static List<ExcelBatchDto> getBatchDtos(int size) {
        List<ExcelBatchDto> excelBatchDtos = Lists.newArrayList();
        for (int i = 0; i < size; i++) {
            excelBatchDtos.add(ExcelBatchDto.builder()
                .orderNumber("M-12345" + i)
                .itemNumber("JC12345")
                .line(i + 1)
                .quantity(1)
                .pos(i + 1 + "")
                .build());
        }
        return excelBatchDtos;
    }

    private ExcelBatchDto createExcelBatchDto(String orderNumber, String districtName, int quantity, String source) {
        ExcelBatchDto dto = new ExcelBatchDto();
        dto.setOrderNumber(orderNumber);
        dto.setDistrictName(districtName);
        dto.setQuantity(quantity);
        dto.setSource(source);
        dto.setFrom("Location1");
        return dto;
    }

    private ExcelBatchDto createExcelBatchDtoWithDepartment(String orderNumber,
        String districtName,
        int quantity,
        String source,
        String department) {
        ExcelBatchDto dto = createExcelBatchDto(orderNumber, districtName, quantity, source);
        dto.setDepartment(department);
        return dto;
    }
}