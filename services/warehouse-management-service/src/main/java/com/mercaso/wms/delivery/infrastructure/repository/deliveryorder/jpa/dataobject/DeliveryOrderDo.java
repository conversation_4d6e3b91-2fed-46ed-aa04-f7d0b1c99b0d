package com.mercaso.wms.delivery.infrastructure.repository.deliveryorder.jpa.dataobject;

import com.fasterxml.jackson.databind.JsonNode;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.DeliveryOrderStatus;
import com.mercaso.wms.delivery.domain.deliveryorder.enums.RescheduleType;
import com.mercaso.wms.delivery.infrastructure.repository.customer.jpa.dataobject.CustomerDo;
import com.mercaso.wms.infrastructure.repository.BaseDo;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.UUID;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.SQLRestriction;
import org.hibernate.type.SqlTypes;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@ToString(exclude = "deliveryOrderItems")
@Table(name = "da_delivery_order")
@SQLDelete(sql = "update da_delivery_order set deleted_at = current_timestamp where id = ? and updated_at = ?")
@SQLRestriction("deleted_at is null")
public class DeliveryOrderDo extends BaseDo {

    @Column(name = "warehouse_id")
    private UUID warehouseId;


    @Column(name = "delivery_task_id")
    private UUID deliveryTaskId;

    @Column(name = "shopify_order_id", length = 50)
    private String shopifyOrderId;

    @Column(name = "order_number", nullable = false, length = 50)
    private String orderNumber;

    @Column(name = "status", length = 30)
    @Enumerated(EnumType.STRING)
    private DeliveryOrderStatus status;

    @Column(name = "delivery_date", length = 30)
    private String deliveryDate;

    @Column(name = "delivery_time_window", length = 100)
    private String deliveryTimeWindow;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "address", columnDefinition = "jsonb")
    private JsonNode address;

    @JoinColumn(name = "customer_id")
    @ManyToOne(fetch = FetchType.LAZY)
    private CustomerDo customer;

    /**
     * A string separated by commas where the value's position corresponds to
     * {@link com.mercaso.wms.delivery.domain.deliveryorder.enums.PaymentType}.
     */
    @Column(name = "payment_type", length = 128)
    private String paymentType;

    @Column(name = "payment_status", length = 30)
    private String paymentStatus;

    @Column(name = "fulfillment_status", length = 30)
    private String fulfillmentStatus;

    @Column(name = "sequence")
    private Integer sequence;

    @Column(name = "plan_arrive_at")
    private Instant planArriveAt;

    @Column(name = "plan_delivery_at")
    private Instant planDeliveryAt;

    @Column(name = "unloaded_at")
    private Instant unloadedAt;

    @Column(name = "in_transit_at")
    private Instant inTransitAt;

    @Column(name = "arrived_at")
    private Instant arrivedAt;

    @Column(name = "delivered_at")
    private Instant deliveredAt;

    @Column(name = "customer_notes", columnDefinition = "text")
    private String customerNotes;

    @Column(name = "notes", columnDefinition = "text")
    private String notes;

    @Column(name = "total_price", precision = 10, scale = 2)
    private BigDecimal totalPrice;

    @Column(name = "adjusted_price")
    private String adjustedPrice;

    @Column(name = "original_total_price", precision = 10, scale = 2)
    private BigDecimal originalTotalPrice;

    @Column(name = "current_total_discounts", precision = 10, scale = 2)
    private BigDecimal currentTotalDiscounts;

    @Column(name = "discount_applications", columnDefinition = "jsonb")
    private String discountApplications;

    @Column(name = "reschedule_type")
    @Enumerated(EnumType.STRING)
    private RescheduleType rescheduleType;

    @OneToMany(mappedBy = "deliveryOrder", fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @OrderBy("line ASC")
    private List<DeliveryOrderItemDo> deliveryOrderItems;

    @Column(name = "rm_order_id")
    private String rmOrderId;
} 