package com.mercaso.wms.infrastructure.slackalert;

import com.mercaso.wms.infrastructure.utils.HttpClientUtils;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class FraudOrderAlert {

    @Value("${slack.fraud-order-alter.webhook}")
    private String slackWebhook;
    @Value("${slack.fraud-order-alter.enable}")
    private boolean enable;

    public static String SHOPIFY_ORDER_URL = "https://admin.shopify.com/store/shop-mercaso/orders/";

    public void sendFraudOrderAlter(String orderNumber, String ip, String deviceId, String orderId, String phoneNumber, String paymentType, String note) {
        if (!enable) {
            return;
        }
        try {
            HttpClientUtils.executePostRequest(slackWebhook, Map.of("text", buildSlackMessage(orderNumber, ip, deviceId, orderId, phoneNumber, paymentType, note)), new HashMap<>(), String.class);
        } catch (Exception e) {
            log.warn("[sendFraudOrderAlter] Failed to send fraud Order alert", e);
        }
    }

    private static String buildSlackMessage(String orderNumber, String ip, String deviceId, String orderId, String phoneNumber, String paymentType, String note) {
        return String.format(
                """
                        :warning: *Suspected fraud order detected!* :warning:
                        *Order Number*: %s
                        *IP Address*: %s
                        *Device ID*: %s
                        *Phone Number*: %s
                        *Payment Type*: %s
                        *Customer Note*: %s
                        Click to view the order details: <%s%s>""",
            StringUtils.isEmpty(orderNumber) ? "" : orderNumber,
            StringUtils.isEmpty(ip) ? "" : ip,
            StringUtils.isEmpty(deviceId) ? "" : deviceId,
            StringUtils.isEmpty(phoneNumber) ? "" : phoneNumber,
            StringUtils.isEmpty(paymentType) ? "" : paymentType,
            StringUtils.isEmpty(note) ? "" : note,
                SHOPIFY_ORDER_URL,
            StringUtils.isEmpty(orderId) ? "null" : orderId
        );
    }

}
