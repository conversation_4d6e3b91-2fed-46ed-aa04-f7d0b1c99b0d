package com.mercaso.wms.delivery.application.queryservice;

import com.mercaso.wms.delivery.application.dto.deliveryorder.DeliveryOrderDto;
import com.mercaso.wms.delivery.application.dto.deliverytask.DeliveryTaskDto;
import com.mercaso.wms.delivery.application.mapper.deliveryorder.DeliveryOrderDtoApplicationMapper;
import com.mercaso.wms.delivery.application.mapper.deliverytask.DeliveryTaskDtoApplicationMapper;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrder;
import com.mercaso.wms.delivery.domain.deliveryorder.DeliveryOrderRepository;
import com.mercaso.wms.delivery.domain.deliverytask.DeliveryTaskRepository;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class DeliveryTaskQueryService {

    private final DeliveryTaskRepository deliveryTaskRepository;
    private final DeliveryTaskDtoApplicationMapper deliveryTaskDtoApplicationMapper;
    private final DeliveryOrderRepository deliveryOrderRepository;
    private final DeliveryOrderDtoApplicationMapper deliveryOrderDtoApplicationMapper;

    public DeliveryTaskDto findById(UUID id) {
        log.info("Finding delivery task by id: {}", id);

        return Optional.ofNullable(deliveryTaskRepository.findById(id))
            .map(task -> {
                DeliveryTaskDto taskDto = deliveryTaskDtoApplicationMapper.domainToDto(task);
                enrichWithSortedOrders(id, taskDto);
                return taskDto;
            })
            .orElseGet(() -> {
                log.warn("Delivery task not found with id: {}", id);
                return null;
            });
    }

    public List<DeliveryTaskDto> findByIds(List<UUID> deliveryTaskIds) {
        log.info("Finding delivery tasks by ids: {}", deliveryTaskIds);

        return deliveryTaskRepository.findByIdIn(deliveryTaskIds).stream()
            .map(deliveryTaskDtoApplicationMapper::domainToDto)
            .toList();
    }

    private void enrichWithSortedOrders(UUID taskId, DeliveryTaskDto taskDto) {
        List<DeliveryOrder> deliveryOrders = deliveryOrderRepository.findAllByDeliveryTaskId(taskId);

        if (CollectionUtils.isEmpty(deliveryOrders)) {
            log.info("No orders found for delivery task id: {}", taskId);
            return;
        }

        List<DeliveryOrderDto> sortedOrderDtos = deliveryOrders.stream()
            .sorted(createSequenceComparator())
            .map(deliveryOrderDtoApplicationMapper::domainToDto)
            .toList();

        taskDto.setDeliveryOrders(sortedOrderDtos);
        log.info("Found {} orders for delivery task id: {}", sortedOrderDtos.size(), taskId);
    }

    /**
     * Creates a comparator for ordering delivery orders by sequence.
     * Orders are sorted in ascending order with null values last.
     *
     * @return Comparator that sorts delivery orders by sequence
     */
    private Comparator<DeliveryOrder> createSequenceComparator() {
        return Comparator.comparing(
            DeliveryOrder::getSequence,
            Comparator.nullsLast(Comparator.naturalOrder())
        );
    }
} 