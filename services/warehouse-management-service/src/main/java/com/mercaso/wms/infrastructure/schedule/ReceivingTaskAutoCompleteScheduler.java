package com.mercaso.wms.infrastructure.schedule;

import com.mercaso.wms.application.service.ReceivingTaskApplicationService;
import com.mercaso.wms.domain.receivingtask.ReceivingTask;
import com.mercaso.wms.domain.receivingtask.ReceivingTaskRepository;
import com.mercaso.wms.domain.receivingtask.enums.ReceivingTaskStatus;
import com.mercaso.wms.infrastructure.config.PgAdvisoryLock;
import com.mercaso.wms.infrastructure.exception.WmsBusinessException;
import com.mercaso.wms.infrastructure.utils.DateUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReceivingTaskAutoCompleteScheduler {

    private final ReceivingTaskRepository receivingTaskRepository;
    private final ReceivingTaskApplicationService receivingTaskApplicationService;
    private final PgAdvisoryLock pgAdvisoryLock;

    private static final int LOCK_KEY = "ReceivingTaskAutoCompleteScheduler.autoCompleteReceivingTasks".hashCode();

    @Scheduled(cron = "0 40 23 * * *", zone = "America/Los_Angeles")
    public void autoCompleteReceivingTasks() {
        try {
            log.info("[autoCompleteReceivingTasks] Scheduler started");

            if (Boolean.FALSE.equals(pgAdvisoryLock.tryLockWithSessionLevel(LOCK_KEY))) {
                log.info("[autoCompleteReceivingTasks] Failed to acquire lock, another instance is running");
                return;
            }

            String deliveryDate = DateUtils.getNextDeliveryDate().format(DateTimeFormatter.ISO_LOCAL_DATE);
            log.info("[autoCompleteReceivingTasks] Processing for delivery date: {}", deliveryDate);

            List<ReceivingTask> receivingTasks = receivingTaskRepository.findByDeliveryDateAndStatus(
                deliveryDate, ReceivingTaskStatus.RECEIVING);
            
            if (CollectionUtils.isEmpty(receivingTasks)) {
                log.info("[autoCompleteReceivingTasks] No receiving tasks found in RECEIVING status for delivery date: {}", deliveryDate);
                return;
            }

            log.info("[autoCompleteReceivingTasks] Found {} tasks to complete", receivingTasks.size());

            for (ReceivingTask task : receivingTasks) {
                try {
                    receivingTaskApplicationService.receive(task.getId());
                } catch (WmsBusinessException e) {
                    log.warn("[autoCompleteReceivingTasks] Business exception while completing task: {}, error: {}", 
                        task.getId(), e.getMessage());
                } catch (Exception e) {
                    log.error("[autoCompleteReceivingTasks] Failed to complete task: {}, error: {}", 
                        task.getId(), e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("[autoCompleteReceivingTasks] Unexpected error occurred: {}", e.getMessage(), e);
        } finally {
            pgAdvisoryLock.unLock(LOCK_KEY);
            log.info("[autoCompleteReceivingTasks] Scheduler completed");
        }
    }
} 